"""
本地版本mem0过期机制演示
展示如何在本地版本中实现记忆过期和自动清理功能
"""

import sys
import os
import time
import datetime
from typing import Dict, List, Any

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

import logging
from mem0 import Memory

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LocalExpirationManager:
    """本地版本的过期记忆管理器"""
    
    def __init__(self):
        """初始化Memory实例"""
        
        self.config = {
            "vector_store": {
                "provider": "chroma",
                "config": {
                    "collection_name": "local_expiration",
                    "path": "./db/local_expiration_db"
                }
            },
            "llm": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "temperature": 0.1,
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            },
            "embedder": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            }
        }
        
        self.memory = Memory.from_config(self.config)
        self.user_id = "local_user"
    
    def add_memory_with_ttl(self, content: str, ttl_hours: int, category: str = "general"):
        """添加带有TTL（生存时间）的记忆"""
        
        # 计算过期时间
        expiration_time = datetime.datetime.now() + datetime.timedelta(hours=ttl_hours)
        
        messages = [
            {"role": "user", "content": content},
            {"role": "assistant", "content": f"记住了这个信息，TTL为{ttl_hours}小时"}
        ]
        
        metadata = {
            "category": category,
            "ttl_hours": ttl_hours,
            "created_at": datetime.datetime.now().isoformat(),
            "expires_at": expiration_time.isoformat(),
            "is_expired": False
        }
        
        print(f"\n📝 添加记忆 (TTL: {ttl_hours}小时)")
        print(f"   内容: {content}")
        print(f"   过期时间: {expiration_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        result = self.memory.add(
            messages,
            user_id=self.user_id,
            metadata=metadata
        )
        
        print(f"✅ 记忆添加成功: {len(result.get('results', []))} 条")
        return result
    
    def add_permanent_memory(self, content: str, category: str = "permanent"):
        """添加永久记忆"""
        
        messages = [
            {"role": "user", "content": content},
            {"role": "assistant", "content": "记住了这个重要信息，永久保存"}
        ]
        
        metadata = {
            "category": category,
            "is_permanent": True,
            "created_at": datetime.datetime.now().isoformat()
        }
        
        print(f"\n💾 添加永久记忆:")
        print(f"   内容: {content}")
        
        result = self.memory.add(
            messages,
            user_id=self.user_id,
            metadata=metadata
        )
        
        print(f"✅ 永久记忆添加成功: {len(result.get('results', []))} 条")
        return result
    
    def is_memory_expired(self, memory_metadata: Dict) -> bool:
        """检查记忆是否过期"""
        if memory_metadata.get('is_permanent', False):
            return False
        
        expires_at_str = memory_metadata.get('expires_at')
        if not expires_at_str:
            return False
        
        try:
            expires_at = datetime.datetime.fromisoformat(expires_at_str)
            return datetime.datetime.now() > expires_at
        except:
            return False
    
    def search_memories_with_expiration(self, query: str):
        """搜索记忆并过滤过期的记忆"""
        print(f"\n🔍 搜索记忆: '{query}' (自动过滤过期记忆)")
        
        # 获取所有搜索结果
        all_results = self.memory.search(query, user_id=self.user_id)
        
        # 过滤过期记忆
        valid_results = []
        expired_count = 0
        
        for result in all_results.get("results", []):
            metadata = result.get('metadata', {})
            if self.is_memory_expired(metadata):
                expired_count += 1
                print(f"  ⏰ 过期记忆已过滤: {result['memory'][:50]}...")
            else:
                valid_results.append(result)
        
        print(f"📋 有效搜索结果 ({len(valid_results)} 条，过滤了 {expired_count} 条过期记忆):")
        for i, result in enumerate(valid_results, 1):
            print(f"  {i}. {result['memory']} (相关性: {result['score']:.2f})")
        
        return {"results": valid_results, "expired_filtered": expired_count}
    
    def get_all_valid_memories(self):
        """获取所有有效记忆（不包括过期的）"""
        print(f"\n📚 所有有效记忆:")
        
        all_memories = self.memory.get_all(user_id=self.user_id)
        valid_memories = []
        expired_count = 0
        
        for memory in all_memories.get("results", []):
            metadata = memory.get('metadata', {})
            if self.is_memory_expired(metadata):
                expired_count += 1
            else:
                valid_memories.append(memory)
        
        for i, mem in enumerate(valid_memories, 1):
            metadata = mem.get('metadata', {})
            category = metadata.get('category', 'unknown')
            if metadata.get('is_permanent'):
                print(f"  {i}. [永久] {mem['memory']}")
            else:
                ttl = metadata.get('ttl_hours', 'unknown')
                print(f"  {i}. [TTL:{ttl}h] {mem['memory']}")
        
        print(f"总计: {len(valid_memories)} 条有效记忆，{expired_count} 条已过期")
        return {"valid": valid_memories, "expired_count": expired_count}
    
    def cleanup_expired_memories(self):
        """清理过期的记忆"""
        print(f"\n🗑️ 清理过期记忆...")
        
        all_memories = self.memory.get_all(user_id=self.user_id)
        expired_memories = []
        
        for memory in all_memories.get("results", []):
            metadata = memory.get('metadata', {})
            if self.is_memory_expired(metadata):
                expired_memories.append(memory)
        
        if not expired_memories:
            print("  没有发现过期记忆")
            return 0
        
        print(f"  发现 {len(expired_memories)} 条过期记忆:")
        deleted_count = 0
        
        for memory in expired_memories:
            try:
                print(f"    删除: {memory['memory'][:50]}...")
                self.memory.delete(memory_id=memory['id'])
                deleted_count += 1
            except Exception as e:
                print(f"    删除失败: {e}")
        
        print(f"✅ 成功删除 {deleted_count} 条过期记忆")
        return deleted_count
    
    def demonstrate_ttl_strategies(self):
        """演示不同的TTL策略"""
        print("\n" + "=" * 50)
        print("⏰ TTL策略演示")
        print("=" * 50)
        
        strategies = [
            ("我现在正在调试一个bug", 0.01, "debugging"),  # 0.6分钟过期，用于快速测试
            ("今天下午3点有个会议", 24, "schedule"),        # 24小时过期
            ("项目deadline是下周五", 168, "project"),       # 7天过期
            ("我的编程偏好是Python", None, "preference")    # 永久记忆
        ]
        
        for content, ttl_hours, category in strategies:
            if ttl_hours is None:
                self.add_permanent_memory(content, category)
            else:
                self.add_memory_with_ttl(content, ttl_hours, category)
    
    def simulate_time_passage(self):
        """模拟时间流逝"""
        print(f"\n⏳ 模拟时间流逝...")
        print("等待1分钟让短期记忆过期...")
        time.sleep(60)  # 等待1分钟
        print("✅ 时间流逝完成")

def main():
    """主演示函数"""
    print("=" * 80)
    print("🧠 本地版本mem0过期机制演示")
    print("=" * 80)
    
    manager = LocalExpirationManager()
    
    # 清理之前的数据
    print("🗑️ 清理之前的测试数据...")
    try:
        manager.memory.delete_all(user_id=manager.user_id)
        print("✅ 清理完成")
    except:
        print("✅ 无需清理")
    
    # 1. 演示TTL策略
    manager.demonstrate_ttl_strategies()
    
    # 2. 查看初始状态
    print("\n" + "=" * 50)
    print("📊 初始记忆状态")
    print("=" * 50)
    manager.get_all_valid_memories()
    
    # 3. 搜索测试
    print("\n" + "=" * 50)
    print("🔍 搜索测试")
    print("=" * 50)
    
    manager.search_memories_with_expiration("调试")
    manager.search_memories_with_expiration("Python")
    manager.search_memories_with_expiration("会议")
    
    # 4. 模拟时间流逝
    print("\n" + "=" * 50)
    print("⏰ 时间流逝模拟")
    print("=" * 50)
    
    print("等待65秒让短期记忆过期...")
    time.sleep(65)
    
    # 5. 过期后的搜索
    print("\n" + "=" * 50)
    print("🔍 过期后搜索")
    print("=" * 50)
    
    manager.search_memories_with_expiration("调试")
    
    # 6. 查看过期后状态
    print("\n" + "=" * 50)
    print("📊 过期后记忆状态")
    print("=" * 50)
    manager.get_all_valid_memories()
    
    # 7. 清理过期记忆
    print("\n" + "=" * 50)
    print("🧹 清理过期记忆")
    print("=" * 50)
    manager.cleanup_expired_memories()
    
    # 8. 最终状态
    print("\n" + "=" * 50)
    print("📊 最终记忆状态")
    print("=" * 50)
    manager.get_all_valid_memories()
    
    print("\n" + "=" * 80)
    print("🎉 本地版本过期机制演示完成！")
    print("📝 实现的功能:")
    print("  ✅ 基于metadata的TTL机制")
    print("  ✅ 搜索时自动过滤过期记忆")
    print("  ✅ 手动清理过期记忆")
    print("  ✅ 永久记忆和临时记忆区分")
    print("  ✅ 灵活的过期时间设置")
    print("=" * 80)

if __name__ == "__main__":
    main()
