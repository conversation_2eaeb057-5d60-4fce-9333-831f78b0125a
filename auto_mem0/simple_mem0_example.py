"""
简化的Mem0使用示例
使用本地qwen3:4b模型的最简单用法
"""

import sys
import os

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

import logging
from mem0 import Memory
from mem0.llms.ollama import OllamaLLM
from mem0.memory.main import Memory as MemoryMain

# 设置详细的日志记录
# logging.basicConfig(
#     level=logging.DEBUG,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
# )
# 设置特定库的日志级别
# logging.getLogger("mem0").setLevel(logging.DEBUG)
# logging.getLogger("ollama").setLevel(logging.DEBUG)
# logging.getLogger("httpx").setLevel(logging.INFO)  # 减少HTTP请求日志


class DebugOllamaLLM(OllamaLLM):
    """继承OllamaLLM来拦截和显示prompt与响应"""

    def generate_response(self, messages, **kwargs):
        print("\n" + "="*80)
        print("🤖 LLM调用详情")
        print("="*80)
        print("📝 发送给LLM的Messages:")
        for i, msg in enumerate(messages, 1):
            print(f"  {i}. Role: {msg.get('role', 'unknown')}")
            content = msg.get('content', '')
            if len(content) > 300:
                print(f"     Content: {content[:300]}...")
                print(f"     (内容被截断，总长度: {len(content)} 字符)")
            else:
                print(f"     Content: {content}")

        print(f"\n🔧 其他参数: {kwargs}")

        # 显示完整的prompt内容
        print("\n📋 完整Prompt内容:")
        for i, msg in enumerate(messages, 1):
            print(f"\n--- Message {i} (Role: {msg.get('role', 'unknown')}) ---")
            print(msg.get('content', ''))

        print("\n⏳ 等待LLM响应...")

        # 调用原始方法
        response = super().generate_response(messages, **kwargs)

        print(f"\n✅ LLM完整响应:")
        print(response)
        print("="*80)
        print()

        return response

def create_local_memory():
    """创建本地Memory对象（带中文prompt和调试功能）"""
    config = {
        "vector_store": {
            "provider": "chroma",
            "config": {
                "collection_name": "my_memories",
                "path": "./db/chroma_db"
            }
        },
        "llm": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:8b",
                "temperature": 0.1,
                "ollama_base_url": "http://10.151.5.243:8088"
            }
        },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:8b",
                "ollama_base_url": "http://10.151.5.243:8088"
            }
        }
    }

    # 创建Memory对象
    memory = Memory.from_config(config)

    # 设置中文prompt模板
    # memory.custom_fact_extraction_prompt = CHINESE_FACT_EXTRACTION_PROMPT
    # memory.custom_update_memory_prompt = CHINESE_MEMORY_UPDATE_PROMPT

    # 替换LLM为调试版本
    from mem0.configs.llms.base import BaseLlmConfig
    llm_config = BaseLlmConfig(**config["llm"]["config"])
    # memory.llm = DebugOllamaLLM(llm_config)

    return memory

def main():
    """简单的使用示例"""
    print("=== 简化的Mem0使用示例 ===\n")
    
    # 创建Memory对象
    memory = create_local_memory()
    user_id = "my_user"

    # 0. 清理现有记忆（可选 - 仅用于演示）
    # 注释掉下面的代码以保留现有记忆
    # print("0. 清理现有记忆...")
    # try:
    #     delete_result = memory.delete_all(user_id=user_id)
    #     print(f"清理结果: {delete_result}")
    # except Exception as e:
    #     print(f"清理时出错: {e}")
    # print()

    # 1. 添加记忆
    print("1. 添加记忆...")
    messages = [
        {"role": "user", "content": "我喜欢喝咖啡，特别是拿铁。我住在北京，是一名程序员。我的手机号是1508888888，我老家是江西赣州的"},
        {"role": "assistant", "content": "记住了你的偏好和信息。"}
    ]

    print(f"准备添加的消息: {messages}")
    result = memory.add(messages, user_id=user_id)
    print(f"添加结果: {result}")

    # 检查是否有错误信息
    if 'results' in result and len(result['results']) == 0:
        print("⚠️  警告: 没有新的记忆被添加")
        print("可能原因:")
        print("- 信息已经存在（重复）")
        print("- LLM没有从消息中提取出新的记忆点")
        print("- 配置问题")
    else:
        print(f"✓ 成功添加了 {len(result.get('results', []))} 条记忆")
    print()
    
    # 2. 搜索记忆
    print("2. 搜索记忆...")
    search_result = memory.search("用户喜欢什么饮品？", user_id=user_id)
    
    for i, result in enumerate(search_result.get("results", []), 1):
        print(f"{i}. {result['memory']} (相关性: {result['score']:.2f})")
    print()
    
    # 3. 获取所有记忆
    print("3. 所有记忆:")
    all_memories = memory.get_all(user_id=user_id)

    # 获取实际的记忆列表
    memories_list = all_memories.get("results", [])

    if memories_list:
        for i, mem in enumerate(memories_list, 1):
            print(f"{i}. {mem['memory']}")
    else:
        print("没有找到记忆")
    print()

    # 4. 添加新的记忆（演示增量添加）
    print("4. 添加新的记忆...")
    new_messages = [
        {"role": "user", "content": "我最近开始学习Python，特别喜欢用FastAPI框架。"},
        {"role": "assistant", "content": "了解了你的学习情况。"}
    ]

    new_result = memory.add(new_messages, user_id=user_id)
    print(f"新添加结果: {new_result}")

    if 'results' in new_result and len(new_result['results']) > 0:
        print(f"✓ 成功添加了 {len(new_result['results'])} 条新记忆")
        for item in new_result['results']:
            print(f"  - {item['memory']}")
    else:
        print("没有添加新记忆（可能信息已存在）")
    print()

    # 5. 清理（可选）
    # memory.delete_all(user_id=user_id)
    # print("记忆已清理")

    print("✓ 示例完成！")

if __name__ == "__main__":
    main()
