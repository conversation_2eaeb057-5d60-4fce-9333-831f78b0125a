"""
测试程序01：基于JSON配置的LLM分类功能验证
验证从JSON文件加载分类配置，使用大模型进行智能分类
"""

import sys
import os
import json
import time

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

from enhanced_memory import create_classifier_function

def test_json_classification():
    """测试基于JSON配置的LLM分类功能"""
    
    print("=" * 60)
    print("🧪 测试程序01：JSON配置LLM分类功能验证")
    print("=" * 60)
    
    # 1. 加载JSON配置
    print("\n📁 步骤1：加载分类配置文件")
    try:
        with open("memory_categories.json", 'r', encoding='utf-8') as f:
            categories_config = json.load(f)
        print(f"✅ 成功加载 {len(categories_config)} 个分类类别")
        
        # 显示前5个类别
        for i, (category, config) in enumerate(list(categories_config.items())[:5], 1):
            description = config.get("description", "")[:40] + "..."
            print(f"  {i}. {category}: {description}")
        print(f"  ... 还有 {len(categories_config) - 5} 个类别")
        
    except Exception as e:
        print(f"❌ 加载配置文件失败: {e}")
        return False
    
    # 2. 创建LLM分类器
    print("\n🤖 步骤2：初始化LLM分类器")
    try:
        llm_config = {
            "model": "qwen3:8b",
            "temperature": 0.1,
            "ollama_base_url": "http://************:8088"
        }
        
        classifier = create_classifier_function(
            categories_config,
            default_category="general",
            llm_config=llm_config
        )
        print("✅ LLM分类器初始化成功")
        
    except Exception as e:
        print(f"❌ LLM分类器初始化失败: {e}")
        return False
    
    # 3. 测试分类功能
    print("\n🔬 步骤3：测试分类功能")
    
    test_cases = [
        "我叫张三，今年25岁，是一名软件工程师",
        "我喜欢喝咖啡，平时爱听古典音乐",
        "下周要完成项目部署，时间很紧张",
        "最近在学习Python深度学习框架",
        "每天早上跑步，保持身体健康",
        "计划投资一些股票基金",
        "明年想去日本旅游",
        "昨晚看了一部很棒的科幻电影"
    ]
    
    results = []
    total_time = 0
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n  测试 {i}: '{test_text}'")
        
        try:
            start_time = time.time()
            category, metadata = classifier(test_text)
            end_time = time.time()
            
            classification_time = end_time - start_time
            total_time += classification_time
            
            confidence = metadata.get("classification_confidence", 0)
            reasoning = metadata.get("classification_reasoning", "")
            method = metadata.get("classification_method", "")
            
            print(f"    📂 类别: {category}")
            print(f"    🎯 置信度: {confidence:.2f}")
            print(f"    🔧 方法: {method}")
            print(f"    ⏱️ 耗时: {classification_time:.2f}秒")
            print(f"    💭 理由: {reasoning[:80]}...")
            
            results.append({
                "text": test_text,
                "category": category,
                "confidence": confidence,
                "method": method,
                "time": classification_time,
                "success": True
            })
            
        except Exception as e:
            print(f"    ❌ 分类失败: {e}")
            results.append({
                "text": test_text,
                "category": "error",
                "confidence": 0,
                "method": "error",
                "time": 0,
                "success": False
            })
    
    # 4. 统计结果
    print("\n📊 步骤4：统计测试结果")
    
    successful_results = [r for r in results if r["success"]]
    failed_results = [r for r in results if not r["success"]]
    
    print(f"  总测试数: {len(test_cases)}")
    print(f"  成功分类: {len(successful_results)}")
    print(f"  失败数量: {len(failed_results)}")
    print(f"  成功率: {len(successful_results)/len(test_cases)*100:.1f}%")
    print(f"  总耗时: {total_time:.2f}秒")
    print(f"  平均耗时: {total_time/len(successful_results):.2f}秒/条" if successful_results else "N/A")
    
    # 5. 分类分布统计
    if successful_results:
        print(f"\n📈 分类分布:")
        category_counts = {}
        for result in successful_results:
            category = result["category"]
            category_counts[category] = category_counts.get(category, 0) + 1
        
        for category, count in sorted(category_counts.items()):
            print(f"  {category}: {count} 条")
    
    # 6. 验证结果
    print(f"\n✅ 测试完成")
    
    if len(successful_results) >= len(test_cases) * 0.8:  # 80%成功率
        print("🎉 测试通过！JSON配置LLM分类功能正常工作")
        return True
    else:
        print("⚠️ 测试未完全通过，部分功能可能存在问题")
        return False

if __name__ == "__main__":
    success = test_json_classification()
    if success:
        print("\n" + "=" * 60)
        print("🎯 测试程序01执行成功")
        print("✅ JSON配置的LLM分类功能验证通过")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 测试程序01执行失败")
        print("🔧 请检查配置和环境设置")
        print("=" * 60)
