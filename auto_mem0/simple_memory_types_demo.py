"""
简化版记忆类型Demo
演示短期记忆、长期记忆和工作记忆的基本使用
"""

import sys
import os
import time
import datetime
from typing import Dict, List, Any

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

import logging
from mem0 import Memory

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleMemoryManager:
    """简化的记忆管理器"""
    
    def __init__(self):
        """初始化不同类型的记忆存储"""
        
        # 基础配置
        base_config = {
            "llm": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "temperature": 0.1,
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            },
            "embedder": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            }
        }
        
        # 短期记忆 - 内存存储，会话结束后消失
        short_term_config = base_config.copy()
        short_term_config["vector_store"] = {
            "provider": "chroma",
            "config": {
                "collection_name": "short_term_simple",
                "path": "./db/:memory:"
            }
        }
        self.short_term_memory = Memory.from_config(short_term_config)
        
        # 工作记忆 - 临时文件存储，任务完成后可清理
        working_config = base_config.copy()
        working_config["vector_store"] = {
            "provider": "chroma",
            "config": {
                "collection_name": "working_simple",
                "path": "./db/temp_working_simple_db"
            }
        }
        self.working_memory = Memory.from_config(working_config)
        
        # 长期记忆 - 持久化存储，永久保存
        long_term_config = base_config.copy()
        long_term_config["vector_store"] = {
            "provider": "chroma",
            "config": {
                "collection_name": "long_term_simple",
                "path": "./db/persistent_long_term_simple_db"
            }
        }
        self.long_term_memory = Memory.from_config(long_term_config)
        
        self.user_id = "simple_user"
    
    def add_short_term_memory(self, content: str, category: str = "general"):
        """添加短期记忆"""
        messages = [
            {"role": "user", "content": content},
            {"role": "assistant", "content": "记住了这个短期信息"}
        ]
        
        metadata = {
            "type": "short_term",
            "category": category,
            "created_at": datetime.datetime.now().isoformat()
        }
        
        print(f"\n📝 添加短期记忆: {content}")
        result = self.short_term_memory.add(messages, user_id=self.user_id, metadata=metadata)
        print(f"✅ 短期记忆添加成功: {len(result.get('results', []))} 条")
        return result
    
    def add_working_memory(self, content: str, category: str = "task"):
        """添加工作记忆"""
        messages = [
            {"role": "user", "content": content},
            {"role": "assistant", "content": "记住了这个工作相关信息"}
        ]
        
        metadata = {
            "type": "working",
            "category": category,
            "created_at": datetime.datetime.now().isoformat()
        }
        
        print(f"\n💼 添加工作记忆: {content}")
        result = self.working_memory.add(messages, user_id=self.user_id, metadata=metadata)
        print(f"✅ 工作记忆添加成功: {len(result.get('results', []))} 条")
        return result
    
    def add_long_term_memory(self, content: str, category: str = "knowledge"):
        """添加长期记忆"""
        messages = [
            {"role": "user", "content": content},
            {"role": "assistant", "content": "记住了这个重要信息"}
        ]
        
        metadata = {
            "type": "long_term",
            "category": category,
            "created_at": datetime.datetime.now().isoformat()
        }
        
        print(f"\n💾 添加长期记忆: {content}")
        result = self.long_term_memory.add(messages, user_id=self.user_id, metadata=metadata)
        print(f"✅ 长期记忆添加成功: {len(result.get('results', []))} 条")
        return result
    
    def search_all_memories(self, query: str):
        """搜索所有类型的记忆"""
        print(f"\n🔍 搜索所有记忆: '{query}'")
        
        # 搜索短期记忆
        print("📋 短期记忆搜索结果:")
        short_results = self.short_term_memory.search(query, user_id=self.user_id)
        for i, result in enumerate(short_results.get("results", [])[:3], 1):
            print(f"  {i}. [短期] {result['memory']} (相关性: {result['score']:.2f})")
        
        # 搜索工作记忆
        print("💼 工作记忆搜索结果:")
        working_results = self.working_memory.search(query, user_id=self.user_id)
        for i, result in enumerate(working_results.get("results", [])[:3], 1):
            print(f"  {i}. [工作] {result['memory']} (相关性: {result['score']:.2f})")
        
        # 搜索长期记忆
        print("💾 长期记忆搜索结果:")
        long_results = self.long_term_memory.search(query, user_id=self.user_id)
        for i, result in enumerate(long_results.get("results", [])[:3], 1):
            print(f"  {i}. [长期] {result['memory']} (相关性: {result['score']:.2f})")
        
        return {
            "short_term": short_results,
            "working": working_results,
            "long_term": long_results
        }
    
    def get_memory_summary(self):
        """获取记忆摘要"""
        print(f"\n📊 记忆摘要:")
        
        try:
            short_memories = self.short_term_memory.get_all(user_id=self.user_id)
            short_count = len(short_memories.get("results", []))
            print(f"  📋 短期记忆: {short_count} 条")
            
            working_memories = self.working_memory.get_all(user_id=self.user_id)
            working_count = len(working_memories.get("results", []))
            print(f"  💼 工作记忆: {working_count} 条")
            
            long_memories = self.long_term_memory.get_all(user_id=self.user_id)
            long_count = len(long_memories.get("results", []))
            print(f"  💾 长期记忆: {long_count} 条")
            
            total = short_count + working_count + long_count
            print(f"  📈 总计: {total} 条记忆")
            
            return {
                "short_term": short_count,
                "working": working_count,
                "long_term": long_count,
                "total": total
            }
        except Exception as e:
            print(f"  ❌ 获取记忆摘要时出错: {e}")
            return {}
    
    def clear_short_term_memory(self):
        """清理短期记忆"""
        print(f"\n🗑️ 清理短期记忆...")
        try:
            result = self.short_term_memory.delete_all(user_id=self.user_id)
            print(f"✅ 短期记忆已清理")
        except Exception as e:
            print(f"❌ 清理短期记忆时出错: {e}")
    
    def clear_working_memory(self):
        """清理工作记忆"""
        print(f"\n🗑️ 清理工作记忆...")
        try:
            result = self.working_memory.delete_all(user_id=self.user_id)
            print(f"✅ 工作记忆已清理")
        except Exception as e:
            print(f"❌ 清理工作记忆时出错: {e}")

def main():
    """主演示函数"""
    print("=" * 80)
    print("🧠 简化版记忆类型管理Demo")
    print("=" * 80)
    
    manager = SimpleMemoryManager()
    
    # 1. 添加不同类型的记忆
    print("\n" + "=" * 50)
    print("1️⃣ 添加不同类型的记忆")
    print("=" * 50)
    
    # 长期记忆 - 用户基本信息和知识
    manager.add_long_term_memory("我叫王五，是一名数据科学家，专门研究机器学习算法", "personal")
    manager.add_long_term_memory("Python是一种高级编程语言，广泛用于数据科学和AI领域", "knowledge")
    
    # 工作记忆 - 当前项目和任务
    manager.add_working_memory("我正在开发一个推荐系统项目，使用协同过滤算法", "project")
    manager.add_working_memory("需要收集用户行为数据，包括点击、购买、评分等", "task")
    
    # 短期记忆 - 当前会话信息
    manager.add_short_term_memory("我现在在调试模型训练代码，遇到了过拟合问题", "current")
    manager.add_short_term_memory("刚才查看了验证集的损失曲线，发现在第50轮开始过拟合", "debugging")
    
    # 2. 查看记忆摘要
    print("\n" + "=" * 50)
    print("2️⃣ 记忆摘要")
    print("=" * 50)
    
    manager.get_memory_summary()
    
    # 3. 搜索测试
    print("\n" + "=" * 50)
    print("3️⃣ 搜索测试")
    print("=" * 50)
    
    # 搜索用户信息
    manager.search_all_memories("用户是谁")
    
    # 搜索技术相关
    manager.search_all_memories("Python 机器学习")
    
    # 搜索当前问题
    manager.search_all_memories("过拟合")
    
    # 4. 添加更多记忆
    print("\n" + "=" * 50)
    print("4️⃣ 添加更多记忆")
    print("=" * 50)
    
    manager.add_short_term_memory("我尝试了早停法，在验证损失不再下降时停止训练", "solution")
    manager.add_working_memory("项目deadline是下周五，需要完成模型优化和部署", "deadline")
    
    # 5. 最终搜索
    print("\n" + "=" * 50)
    print("5️⃣ 最终搜索测试")
    print("=" * 50)
    
    manager.search_all_memories("项目进展")
    
    # 6. 清理演示
    print("\n" + "=" * 50)
    print("6️⃣ 记忆清理演示")
    print("=" * 50)
    
    print("清理前的记忆状态:")
    manager.get_memory_summary()
    
    # 清理短期记忆
    manager.clear_short_term_memory()
    
    print("\n清理短期记忆后的状态:")
    manager.get_memory_summary()
    
    print("\n验证长期记忆是否保留:")
    manager.search_all_memories("王五")
    
    print("\n" + "=" * 80)
    print("🎉 简化版记忆管理Demo完成！")
    print("📝 演示要点:")
    print("  ✅ 短期记忆：临时信息，可以清理")
    print("  ✅ 工作记忆：任务相关，项目周期内保留")
    print("  ✅ 长期记忆：重要信息，永久保存")
    print("  ✅ 智能搜索：跨所有记忆类型搜索")
    print("  ✅ 分类管理：不同类型记忆分别存储")
    print("=" * 80)

if __name__ == "__main__":
    main()
