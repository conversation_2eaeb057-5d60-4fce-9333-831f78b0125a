# 快速开始指南

## 🚀 5分钟快速体验短期记忆和长期记忆

### 前置条件

1. **确保Ollama服务运行**
   ```bash
   # 检查Ollama是否运行
   curl http://10.151.5.243:8088/api/tags
   ```

2. **确保模型可用**
   ```bash
   # 检查qwen3:8b模型是否可用
   ollama list | grep qwen3
   ```

### 快速运行

#### 方式一：基础演示（推荐新手）

```bash
cd auto_mem0
python memory_types_demo.py
```

**预期结果：**
- ✅ 添加长期记忆：用户基本信息（姓名、职业、偏好）
- ✅ 添加短期记忆：当前会话信息（调试问题）
- ✅ 智能搜索：分别搜索长期和短期记忆
- ✅ 记忆清理：短期记忆可清除，长期记忆保留

#### 方式二：简化版演示（推荐进阶）

```bash
cd auto_mem0
python simple_memory_types_demo.py
```

**预期结果：**
- ✅ 三种记忆类型：短期、工作、长期
- ✅ 跨类型搜索：一次搜索所有记忆类型
- ✅ 记忆统计：实时显示各类型记忆数量
- ✅ 选择性清理：保留重要记忆

### 核心概念理解

#### 🧠 记忆类型对比

| 记忆类型 | 存储方式 | 生命周期 | 用途示例 |
|---------|----------|----------|----------|
| **短期记忆** | 内存 | 会话级别 | 当前对话内容、临时状态 |
| **工作记忆** | 临时文件 | 任务周期 | 项目进度、当前任务 |
| **长期记忆** | 持久化 | 永久保存 | 用户偏好、重要知识 |

#### 🔍 搜索示例

```python
# 搜索用户基本信息（长期记忆）
manager.search_long_term_memory("用户的职业是什么？")
# 结果：软件工程师

# 搜索当前问题（短期记忆）
manager.search_short_term_memory("用户现在在做什么？")
# 结果：调试Python内存泄漏问题

# 跨类型搜索
manager.search_all_memories("Python")
# 结果：从所有记忆类型中找到相关信息
```

### 实际运行效果

#### 记忆添加效果
```
💾 添加长期记忆: 我叫王五，是一名数据科学家
✅ 长期记忆添加成功: 3 条

💼 添加工作记忆: 我正在开发推荐系统项目
✅ 工作记忆添加成功: 1 条

📝 添加短期记忆: 我现在在调试过拟合问题
✅ 短期记忆添加成功: 2 条
```

#### 搜索效果
```
🔍 搜索所有记忆: '用户是谁'

📋 短期记忆搜索结果:
  1. [短期] 查看了验证集的损失曲线 (相关性: 4937.72)

💼 工作记忆搜索结果:
  1. [工作] 正在开发一个推荐系统项目 (相关性: 5670.97)

💾 长期记忆搜索结果:
  1. [长期] Name is 王五 (相关性: 7698.85)
```

#### 记忆统计
```
📊 记忆摘要:
  📋 短期记忆: 2 条
  💼 工作记忆: 3 条
  💾 长期记忆: 4 条
  📈 总计: 9 条记忆
```

### 自定义使用

#### 基础API使用

```python
from memory_types_demo import MemoryTypesDemo

# 创建记忆管理器
demo = MemoryTypesDemo()

# 添加长期记忆
demo.add_long_term_memory([
    {"role": "user", "content": "我喜欢喝咖啡"},
    {"role": "assistant", "content": "记住了你的偏好"}
], {"category": "preferences"})

# 添加短期记忆
demo.add_short_term_memory([
    {"role": "user", "content": "我现在在写代码"},
    {"role": "assistant", "content": "了解你的当前状态"}
], {"category": "current_activity"})

# 搜索记忆
results = demo.search_long_term_memory("用户喜欢什么？")
```

#### 简化API使用

```python
from simple_memory_types_demo import SimpleMemoryManager

# 创建管理器
manager = SimpleMemoryManager()

# 添加不同类型记忆
manager.add_long_term_memory("我是数据科学家", "personal")
manager.add_working_memory("正在开发推荐系统", "project")
manager.add_short_term_memory("调试过拟合问题", "debugging")

# 搜索所有记忆
results = manager.search_all_memories("机器学习")

# 查看统计
stats = manager.get_memory_summary()
```

### 常见问题

#### Q: 为什么有些记忆添加失败？
A: 可能是元数据格式问题，确保metadata中的值为基本数据类型。

#### Q: 搜索结果为空怎么办？
A: 检查模型是否正确提取了记忆，可以查看日志中的LLM响应。

#### Q: 如何实现自动过期？
A: 在metadata中添加过期时间，然后定期运行清理函数。

#### Q: 可以修改存储路径吗？
A: 可以，修改配置中的`path`参数即可。

### 下一步

1. **阅读完整文档**：查看 `README_memory_demo.md`
2. **自定义配置**：修改模型地址、存储路径等
3. **扩展功能**：添加过期机制、优先级管理等
4. **集成应用**：将记忆管理集成到你的AI应用中

### 技术支持

如果遇到问题，请检查：
1. Ollama服务状态
2. 模型可用性
3. 网络连接
4. 磁盘空间
5. 日志错误信息

---

🎉 **恭喜！你已经成功体验了mem0的短期记忆和长期记忆功能！**
