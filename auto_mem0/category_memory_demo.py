"""
记忆分类管理Demo
展示如何在mem0中实现记忆的分类管理，包括类别验证、自动分类和按类别检索
"""

import sys
import os
import time
import datetime
from typing import Dict, List, Any, Optional
from enum import Enum

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

import logging
from mem0 import Memory

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MemoryCategory(Enum):
    """记忆类别枚举"""
    PERSONAL_INFO = "personal_info"          # 个人信息
    PREFERENCES = "preferences"              # 偏好设置
    WORK_TASKS = "work_tasks"               # 工作任务
    LEARNING = "learning"                   # 学习内容
    CONVERSATIONS = "conversations"          # 对话记录
    REMINDERS = "reminders"                 # 提醒事项
    TECHNICAL_KNOWLEDGE = "technical_knowledge"  # 技术知识
    HEALTH = "health"                       # 健康相关
    FINANCE = "finance"                     # 财务相关
    GENERAL = "general"                     # 通用类别

class CategoryMemoryManager:
    """带有分类管理功能的记忆管理器"""
    
    def __init__(self, allowed_categories: List[MemoryCategory] = None):
        """
        初始化记忆管理器
        
        Args:
            allowed_categories: 允许的记忆类别列表，如果为None则允许所有类别
        """
        # 设置允许的记忆类别
        if allowed_categories is None:
            self.allowed_categories = list(MemoryCategory)
        else:
            self.allowed_categories = allowed_categories
        
        print(f"📋 初始化记忆系统，支持的类别:")
        for i, category in enumerate(self.allowed_categories, 1):
            print(f"  {i}. {category.value}")
        
        # 初始化mem0配置
        self.config = {
            "vector_store": {
                "provider": "chroma",
                "config": {
                    "collection_name": "category_memory",
                    "path": "./db/category_memory_db"
                }
            },
            "llm": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "temperature": 0.1,
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            },
            "embedder": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            }
        }
        
        self.memory = Memory.from_config(self.config)
        self.user_id = "category_user"
        
        # 类别关键词映射
        self.category_keywords = {
            MemoryCategory.PERSONAL_INFO: ["我叫", "我是", "我的名字", "年龄", "住在", "出生", "家庭"],
            MemoryCategory.PREFERENCES: ["喜欢", "不喜欢", "偏好", "爱好", "讨厌", "最爱", "倾向于"],
            MemoryCategory.WORK_TASKS: ["工作", "任务", "项目", "deadline", "会议", "开发", "同事", "老板"],
            MemoryCategory.LEARNING: ["学习", "课程", "教程", "知识", "技能", "培训", "考试", "证书"],
            MemoryCategory.CONVERSATIONS: ["聊天", "对话", "讨论", "交流", "谈话"],
            MemoryCategory.REMINDERS: ["提醒", "记得", "别忘了", "明天", "下周", "约会", "安排"],
            MemoryCategory.TECHNICAL_KNOWLEDGE: ["编程", "代码", "算法", "数据库", "API", "框架", "技术", "开发"],
            MemoryCategory.HEALTH: ["健康", "医生", "药物", "锻炼", "饮食", "睡眠", "体重"],
            MemoryCategory.FINANCE: ["钱", "投资", "理财", "银行", "贷款", "收入", "支出", "预算"],
            MemoryCategory.GENERAL: []  # 通用类别没有特定关键词
        }
    
    def validate_category(self, category: MemoryCategory) -> bool:
        """验证类别是否在允许的类别列表中"""
        return category in self.allowed_categories
    
    def auto_classify_content(self, content: str) -> MemoryCategory:
        """
        基于内容自动分类记忆
        使用关键词匹配进行分类，实际应用中可以使用LLM进行更智能的分类
        """
        content_lower = content.lower()
        
        # 计算每个类别的匹配分数
        category_scores = {}
        for category, keywords in self.category_keywords.items():
            if category == MemoryCategory.GENERAL:
                continue
            
            score = sum(1 for keyword in keywords if keyword in content_lower)
            if score > 0:
                category_scores[category] = score
        
        # 返回得分最高的类别，如果没有匹配则返回通用类别
        if category_scores:
            best_category = max(category_scores, key=category_scores.get)
            return best_category
        else:
            return MemoryCategory.GENERAL
    
    def add_memory_from_conversation(self, messages: List[Dict], category: Optional[MemoryCategory] = None,
                                   importance: str = "medium", ttl_hours: Optional[int] = None) -> Dict:
        """
        从对话中提取记忆并分类存储

        Args:
            messages: 对话消息列表
            category: 记忆类别，如果为None则自动分类
            importance: 重要性级别 (low, medium, high, critical)
            ttl_hours: 生存时间（小时），如果为None则永久保存
        """
        print(f"\n🔄 从对话中提取记忆...")
        print("对话内容:")
        for msg in messages:
            print(f"  {msg['role']}: {msg['content']}")

        # 第一步：让mem0提取记忆内容（不指定类别）
        temp_result = self.memory.add(messages, user_id=self.user_id, infer=True)

        if not temp_result or not temp_result.get('results'):
            print("❌ 未能从对话中提取到记忆")
            return None

        print(f"\n📋 提取到的记忆内容:")
        extracted_memories = []

        # 获取提取出的记忆
        for i, result in enumerate(temp_result.get('results', []), 1):
            memory_text = result.get('memory', '')
            memory_id = result.get('id', '')
            print(f"  {i}. {memory_text}")
            extracted_memories.append({
                'text': memory_text,
                'id': memory_id,
                'original_result': result
            })

        # 第二步：删除临时记忆
        print(f"\n🗑️ 删除临时记忆...")
        for memory in extracted_memories:
            try:
                self.memory.delete(memory_id=memory['id'])
            except Exception as e:
                print(f"删除临时记忆失败: {e}")

        # 第三步：对每个提取出的记忆进行分类和重新存储
        final_results = []
        for memory in extracted_memories:
            memory_text = memory['text']

            # 自动分类（如果未指定类别）
            if category is None:
                auto_category = self.auto_classify_content(memory_text)
                print(f"🤖 '{memory_text}' 自动分类为: {auto_category.value}")
            else:
                auto_category = category
                print(f"📂 '{memory_text}' 指定类别: {auto_category.value}")

            # 验证类别
            if not self.validate_category(auto_category):
                print(f"❌ 错误：类别 '{auto_category.value}' 不在允许的类别列表中")
                print(f"   允许的类别: {[c.value for c in self.allowed_categories]}")
                print(f"   跳过记忆: {memory_text}")
                continue

            # 重新构建消息
            classified_messages = [
                {"role": "user", "content": f"记住这个信息: {memory_text}"},
                {"role": "assistant", "content": f"好的，我记住了这个{auto_category.value}类别的信息"}
            ]

            # 构建元数据
            metadata = {
                "category": auto_category.value,
                "importance": importance,
                "created_at": datetime.datetime.now().isoformat(),
                "auto_classified": category is None,
                "extracted_from_conversation": True
            }

            # 设置TTL
            if ttl_hours:
                expiration_time = datetime.datetime.now() + datetime.timedelta(hours=ttl_hours)
                metadata["ttl_hours"] = ttl_hours
                metadata["expires_at"] = expiration_time.isoformat()

            print(f"\n📝 重新存储分类记忆:")
            print(f"   内容: {memory_text}")
            print(f"   📂 类别: {auto_category.value}")
            print(f"   ⭐ 重要性: {importance}")
            if ttl_hours:
                print(f"   ⏰ TTL: {ttl_hours}小时")

            # 重新添加记忆（不使用infer，直接存储）
            result = self.memory.add(classified_messages, user_id=self.user_id, metadata=metadata, infer=False)
            final_results.append(result)
            print(f"✅ 分类记忆存储成功: {len(result.get('results', []))} 条")

        return {"results": final_results, "extracted_count": len(extracted_memories)}

    def add_memory(self, content: str, category: Optional[MemoryCategory] = None,
                   importance: str = "medium", ttl_hours: Optional[int] = None) -> Dict:
        """
        直接添加记忆到指定类别（用于已知具体内容的情况）

        Args:
            content: 记忆内容
            category: 记忆类别，如果为None则自动分类
            importance: 重要性级别 (low, medium, high, critical)
            ttl_hours: 生存时间（小时），如果为None则永久保存
        """
        # 自动分类（如果未指定类别）
        if category is None:
            category = self.auto_classify_content(content)
            print(f"🤖 自动分类结果: {category.value}")

        # 验证类别
        if not self.validate_category(category):
            print(f"❌ 错误：类别 '{category.value}' 不在允许的类别列表中")
            print(f"   允许的类别: {[c.value for c in self.allowed_categories]}")
            return None

        # 构建消息
        messages = [
            {"role": "user", "content": content},
            {"role": "assistant", "content": f"记住了这个{category.value}类别的信息"}
        ]

        # 构建元数据
        metadata = {
            "category": category.value,
            "importance": importance,
            "created_at": datetime.datetime.now().isoformat(),
            "auto_classified": category is None
        }

        # 设置TTL
        if ttl_hours:
            expiration_time = datetime.datetime.now() + datetime.timedelta(hours=ttl_hours)
            metadata["ttl_hours"] = ttl_hours
            metadata["expires_at"] = expiration_time.isoformat()

        print(f"\n📝 添加记忆:")
        print(f"   内容: {content}")
        print(f"   📂 类别: {category.value}")
        print(f"   ⭐ 重要性: {importance}")
        if ttl_hours:
            print(f"   ⏰ TTL: {ttl_hours}小时")

        # 添加记忆
        result = self.memory.add(messages, user_id=self.user_id, metadata=metadata)
        print(f"✅ 记忆添加成功: {len(result.get('results', []))} 条")

        return result
    
    def search_by_category(self, query: str, categories: List[MemoryCategory] = None) -> Dict:
        """
        按类别搜索记忆
        
        Args:
            query: 搜索查询
            categories: 要搜索的类别列表，如果为None则搜索所有类别
        """
        print(f"\n🔍 搜索记忆: '{query}'")
        if categories:
            print(f"   限制类别: {[c.value for c in categories]}")
        
        # 获取所有搜索结果
        all_results = self.memory.search(query, user_id=self.user_id)
        
        # 按类别过滤结果
        filtered_results = []
        for result in all_results.get("results", []):
            metadata = result.get('metadata', {})
            result_category = metadata.get('category', 'unknown')
            
            # 检查类别过滤
            if categories:
                if not any(c.value == result_category for c in categories):
                    continue
            
            # 检查是否过期
            if self._is_memory_expired(metadata):
                continue
            
            # 添加类别信息到结果中
            result['category'] = result_category
            result['importance'] = metadata.get('importance', 'unknown')
            filtered_results.append(result)
        
        # 按重要性和相关性排序
        importance_order = {"critical": 4, "high": 3, "medium": 2, "low": 1}
        filtered_results.sort(key=lambda x: (
            -importance_order.get(x.get('importance', 'medium'), 2),
            -x.get('score', 0)
        ))
        
        print(f"📋 搜索结果 ({len(filtered_results)} 条):")
        for i, result in enumerate(filtered_results, 1):
            category = result.get('category', 'unknown')
            importance = result.get('importance', 'unknown')
            score = result.get('score', 0)
            print(f"  {i}. [{category}] {result['memory']} (重要性:{importance}, 相关性:{score:.2f})")
        
        return {"results": filtered_results}
    
    def get_memories_by_category(self, category: MemoryCategory) -> Dict:
        """获取指定类别的所有记忆"""
        print(f"\n📂 获取类别 '{category.value}' 的所有记忆:")
        
        all_memories = self.memory.get_all(user_id=self.user_id)
        category_memories = []
        
        for memory in all_memories.get("results", []):
            metadata = memory.get('metadata', {})
            memory_category = metadata.get('category', '')
            
            if memory_category == category.value and not self._is_memory_expired(metadata):
                memory['category'] = memory_category
                memory['importance'] = metadata.get('importance', 'unknown')
                category_memories.append(memory)
        
        for i, mem in enumerate(category_memories, 1):
            importance = mem.get('importance', 'unknown')
            print(f"  {i}. {mem['memory']} (重要性: {importance})")
        
        print(f"总计: {len(category_memories)} 条记忆")
        return {"results": category_memories}
    
    def get_category_statistics(self) -> Dict:
        """获取各类别的记忆统计信息"""
        print(f"\n📊 记忆分类统计:")
        
        all_memories = self.memory.get_all(user_id=self.user_id)
        category_stats = {}
        
        for memory in all_memories.get("results", []):
            metadata = memory.get('metadata', {})
            if self._is_memory_expired(metadata):
                continue
                
            category = metadata.get('category', 'unknown')
            importance = metadata.get('importance', 'medium')
            
            if category not in category_stats:
                category_stats[category] = {"total": 0, "by_importance": {}}
            
            category_stats[category]["total"] += 1
            if importance not in category_stats[category]["by_importance"]:
                category_stats[category]["by_importance"][importance] = 0
            category_stats[category]["by_importance"][importance] += 1
        
        for category, stats in category_stats.items():
            print(f"  📂 {category}: {stats['total']} 条")
            for importance, count in stats["by_importance"].items():
                print(f"    - {importance}: {count} 条")
        
        return category_stats
    
    def _is_memory_expired(self, metadata: Dict) -> bool:
        """检查记忆是否过期"""
        expires_at_str = metadata.get('expires_at')
        if not expires_at_str:
            return False
        
        try:
            expires_at = datetime.datetime.fromisoformat(expires_at_str)
            return datetime.datetime.now() > expires_at
        except:
            return False
    
    def cleanup_category(self, category: MemoryCategory):
        """清理指定类别的所有记忆"""
        print(f"\n🗑️ 清理类别 '{category.value}' 的记忆...")
        
        all_memories = self.memory.get_all(user_id=self.user_id)
        deleted_count = 0
        
        for memory in all_memories.get("results", []):
            metadata = memory.get('metadata', {})
            memory_category = metadata.get('category', '')
            
            if memory_category == category.value:
                try:
                    self.memory.delete(memory_id=memory['id'])
                    deleted_count += 1
                    print(f"  删除: {memory['memory'][:50]}...")
                except Exception as e:
                    print(f"  删除失败: {e}")
        
        print(f"✅ 成功删除 {deleted_count} 条 '{category.value}' 类别的记忆")
        return deleted_count

def main():
    """主演示函数"""
    print("=" * 80)
    print("🧠 记忆分类管理Demo")
    print("=" * 80)
    
    # 初始化记忆管理器，只允许特定类别
    allowed_categories = [
        MemoryCategory.PERSONAL_INFO,
        MemoryCategory.PREFERENCES,
        MemoryCategory.WORK_TASKS,
        MemoryCategory.TECHNICAL_KNOWLEDGE,
        MemoryCategory.GENERAL
    ]
    
    manager = CategoryMemoryManager(allowed_categories)
    
    # 清理之前的数据
    print("\n🗑️ 清理之前的测试数据...")
    try:
        manager.memory.delete_all(user_id=manager.user_id)
        print("✅ 清理完成")
    except:
        print("✅ 无需清理")
    
    # 1. 添加不同类别的记忆
    print("\n" + "=" * 50)
    print("1️⃣ 添加不同类别的记忆")
    print("=" * 50)
    
    # 手动指定类别
    manager.add_memory(
        "我叫张三，是一名软件工程师，住在北京",
        category=MemoryCategory.PERSONAL_INFO,
        importance="high"
    )
    
    # 自动分类
    manager.add_memory("我喜欢喝咖啡，不喜欢喝茶", importance="medium")
    manager.add_memory("我正在开发一个Python项目，deadline是下周五", importance="high")
    manager.add_memory("我需要学习Docker和Kubernetes技术", importance="medium")
    
    # 尝试添加不允许的类别
    print("\n🚫 尝试添加不允许的类别:")
    manager.add_memory("我今天去看了医生", category=MemoryCategory.HEALTH)
    
    # 2. 查看分类统计
    print("\n" + "=" * 50)
    print("2️⃣ 记忆分类统计")
    print("=" * 50)
    manager.get_category_statistics()
    
    # 3. 按类别搜索
    print("\n" + "=" * 50)
    print("3️⃣ 按类别搜索")
    print("=" * 50)
    
    # 搜索所有类别
    manager.search_by_category("张三")
    
    # 只搜索工作相关
    manager.search_by_category("项目", [MemoryCategory.WORK_TASKS])
    
    # 4. 获取特定类别的记忆
    print("\n" + "=" * 50)
    print("4️⃣ 获取特定类别记忆")
    print("=" * 50)
    
    manager.get_memories_by_category(MemoryCategory.PREFERENCES)
    manager.get_memories_by_category(MemoryCategory.WORK_TASKS)
    
    # 5. 添加带TTL的记忆
    print("\n" + "=" * 50)
    print("5️⃣ 添加临时记忆")
    print("=" * 50)
    
    manager.add_memory(
        "临时提醒：今天下午3点开会",
        category=MemoryCategory.WORK_TASKS,
        importance="critical",
        ttl_hours=0.01  # 36秒后过期
    )
    
    # 6. 等待过期并测试
    print("\n等待40秒测试TTL功能...")
    time.sleep(40)
    
    print("\n过期后搜索:")
    manager.search_by_category("开会", [MemoryCategory.WORK_TASKS])
    
    # 7. 最终统计
    print("\n" + "=" * 50)
    print("6️⃣ 最终统计")
    print("=" * 50)
    manager.get_category_statistics()
    
    print("\n" + "=" * 80)
    print("🎉 记忆分类管理Demo完成！")
    print("📝 实现的功能:")
    print("  ✅ 初始化时设置允许的记忆类别")
    print("  ✅ 添加记忆时验证类别")
    print("  ✅ 自动分类功能")
    print("  ✅ 按类别搜索和检索")
    print("  ✅ 返回记忆时包含类别信息")
    print("  ✅ 记忆分类统计")
    print("  ✅ 类别级别的清理功能")
    print("=" * 80)

if __name__ == "__main__":
    main()
