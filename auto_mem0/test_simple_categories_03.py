"""
测试程序03：简单的新分类配置验证
快速验证更新后的分类配置是否正确加载
"""

import sys
import os
import json

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

def test_categories_config():
    """测试分类配置文件"""
    
    print("=" * 60)
    print("🧪 测试程序03：简单分类配置验证")
    print("=" * 60)
    
    # 1. 加载分类配置
    print("\n📁 步骤1：加载分类配置文件")
    try:
        with open("memory_categories.json", 'r', encoding='utf-8') as f:
            categories_config = json.load(f)
        print(f"✅ 成功加载 {len(categories_config)} 个分类类别")
        
    except Exception as e:
        print(f"❌ 加载配置文件失败: {e}")
        return False
    
    # 2. 验证配置结构
    print("\n🔍 步骤2：验证配置结构")
    
    expected_categories = [
        "food",
        "beverage", 
        "natural_landscape_preference",
        "human_landscape_preference",
        "entertainment_landscape_preference",
        "travel_activity",
        "sport_team",
        "athlete",
        "sport_type",
        "news_topic",
        "news_type",
        "entertainment_venues_preference"
    ]
    
    missing_categories = []
    for category in expected_categories:
        if category not in categories_config:
            missing_categories.append(category)
    
    if missing_categories:
        print(f"❌ 缺少以下分类: {missing_categories}")
        return False
    else:
        print("✅ 所有期望的分类都存在")
    
    # 3. 显示所有分类
    print("\n📋 步骤3：显示所有分类详情")
    
    category_mapping = {
        "food": "美食偏好",
        "beverage": "饮品偏好", 
        "natural_landscape_preference": "自然景观偏好",
        "human_landscape_preference": "人文景观偏好",
        "entertainment_landscape_preference": "娱乐景观偏好",
        "travel_activity": "游玩偏好",
        "sport_team": "球队偏好",
        "athlete": "球员偏好",
        "sport_type": "体育类型偏好",
        "news_topic": "新闻话题偏好",
        "news_type": "新闻类型偏好",
        "entertainment_venues_preference": "娱乐场所偏好"
    }
    
    for i, (category, config) in enumerate(categories_config.items(), 1):
        chinese_name = category_mapping.get(category, category)
        description = config.get("description", "")
        examples = config.get("examples", "")
        
        print(f"\n  {i}. {category} ({chinese_name})")
        print(f"     📖 描述: {description}")
        print(f"     💡 示例: {examples}")
        
        # 验证必要字段
        if not description:
            print(f"     ⚠️ 警告: 缺少描述")
        if not examples:
            print(f"     ⚠️ 警告: 缺少示例")
    
    # 4. 测试分类器创建
    print("\n🤖 步骤4：测试分类器创建")
    try:
        from enhanced_memory import create_classifier_function
        
        llm_config = {
            "model": "qwen3:8b",
            "temperature": 0.1,
            "ollama_base_url": "http://************:8088"
        }
        
        classifier = create_classifier_function(
            categories_config,
            default_category="food",
            llm_config=llm_config
        )
        print("✅ 分类器创建成功")
        
        # 简单测试一个分类
        print("\n🔬 步骤5：简单分类测试")
        test_text = "我特别喜欢吃川菜"
        try:
            category, metadata = classifier(test_text)
            confidence = metadata.get("classification_confidence", 0)
            reasoning = metadata.get("classification_reasoning", "")
            
            print(f"  测试文本: '{test_text}'")
            print(f"  分类结果: {category}")
            print(f"  置信度: {confidence:.2f}")
            print(f"  理由: {reasoning[:100]}...")
            print("✅ 分类测试成功")
            
        except Exception as e:
            print(f"❌ 分类测试失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 分类器创建失败: {e}")
        return False
    
    print(f"\n✅ 所有测试通过")
    return True

if __name__ == "__main__":
    success = test_categories_config()
    if success:
        print("\n" + "=" * 60)
        print("🎯 测试程序03执行成功")
        print("✅ 新分类配置验证通过")
        print("📝 配置包含12个偏好分类：")
        print("   🍜 美食偏好、🥤 饮品偏好")
        print("   🏔️ 自然景观、🏛️ 人文景观、🎢 娱乐景观")
        print("   ✈️ 游玩偏好、⚽ 球队偏好、🏃 球员偏好")
        print("   🏀 体育类型、📰 新闻话题、📺 新闻类型")
        print("   🎭 娱乐场所偏好")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 测试程序03执行失败")
        print("🔧 请检查配置文件和环境设置")
        print("=" * 60)
