"""
增强的Memory类
将memory.add()拆分成两个独立接口：extract_memories()和store_memories()
这样可以在外部添加记忆类别，然后再调用存储接口
"""

import sys
import os
import json
import uuid
import hashlib
import datetime
import pytz
from typing import Dict, List, Any, Optional
from copy import deepcopy

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

import logging
from mem0 import Memory
from mem0.memory.utils import parse_messages, get_fact_retrieval_messages, remove_code_blocks

logger = logging.getLogger(__name__)

class ExtractedMemory:
    """提取出的记忆对象"""

    def __init__(self, text: str, memory_id: str = None, event: str = "ADD",
                 old_memory: str = None, embeddings: List[float] = None):
        self.text = text
        self.memory_id = memory_id or str(uuid.uuid4())
        self.event = event  # ADD, UPDATE, DELETE, NONE, GET
        self.old_memory = old_memory
        self.embeddings = embeddings
        self.category = None  # 外部设置的类别
        self.metadata = {}    # 外部设置的元数据
        self.score = None     # 相关性分数（用于GET事件）
    
    def set_category(self, category: str):
        """设置记忆类别"""
        self.category = category
        self.metadata["category"] = category
        return self
    
    def set_metadata(self, metadata: Dict[str, Any]):
        """设置记忆元数据"""
        self.metadata.update(metadata)
        return self
    
    def to_dict(self):
        """转换为字典格式"""
        result = {
            "id": self.memory_id,
            "memory": self.text,  # 使用memory字段名，与mem0保持一致
            "event": self.event,
            "category": self.category,
            "metadata": self.metadata
        }

        if self.old_memory:
            result["old_memory"] = self.old_memory
        if self.score is not None:
            result["score"] = self.score

        return result

class EnhancedMemory(Memory):
    """增强的Memory类，支持分离的提取和存储操作"""
    
    def __init__(self, config=None):
        """初始化增强的Memory类"""
        super().__init__(config)
    
    def extract_memories(self, messages, *, user_id: Optional[str] = None, 
                        agent_id: Optional[str] = None, run_id: Optional[str] = None) -> List[ExtractedMemory]:
        """
        从对话中提取记忆内容，但不存储
        
        Args:
            messages: 对话消息
            user_id: 用户ID
            agent_id: 代理ID  
            run_id: 运行ID
            
        Returns:
            List[ExtractedMemory]: 提取出的记忆列表
        """
        print(f"🧠 开始提取记忆内容...")
        
        # 处理消息格式
        if isinstance(messages, str):
            messages = [{"role": "user", "content": messages}]
        elif isinstance(messages, dict):
            messages = [messages]
        elif not isinstance(messages, list):
            raise ValueError("messages must be str, dict, or list[dict]")
        
        # 构建过滤器
        from mem0.memory.main import _build_filters_and_metadata
        processed_metadata, effective_filters = _build_filters_and_metadata(
            user_id=user_id,
            agent_id=agent_id,
            run_id=run_id,
            input_metadata={}
        )
        
        # 解析消息
        parsed_messages = parse_messages(messages)
        
        # 使用LLM提取事实
        if self.config.custom_fact_extraction_prompt:
            system_prompt = self.config.custom_fact_extraction_prompt
            user_prompt = f"Input:\n{parsed_messages}"
        else:
            system_prompt, user_prompt = get_fact_retrieval_messages(parsed_messages)
        
        response = self.llm.generate_response(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            response_format={"type": "json_object"},
        )
        
        try:
            response = remove_code_blocks(response)
            new_retrieved_facts = json.loads(response)["facts"]
        except Exception as e:
            logger.error(f"Error in new_retrieved_facts: {e}")
            new_retrieved_facts = []
        
        if not new_retrieved_facts:
            logger.debug("No new facts retrieved from input.")
            return []
        
        print(f"✅ 成功提取到 {len(new_retrieved_facts)} 条记忆")
        
        # 获取现有记忆进行比较（用于确定event类型）
        existing_memories = self.get_all(user_id=user_id, agent_id=agent_id, run_id=run_id)
        existing_texts = {mem.get('memory', '') for mem in existing_memories.get('results', [])}

        # 为每个提取的事实创建ExtractedMemory对象
        extracted_memories = []
        for fact in new_retrieved_facts:
            # 生成嵌入向量
            embeddings = self.embedding_model.embed(fact, "add")

            # 确定事件类型
            event_type = "UPDATE" if fact in existing_texts else "ADD"

            extracted_memory = ExtractedMemory(
                text=fact,
                embeddings=embeddings,
                event=event_type
            )
            extracted_memories.append(extracted_memory)
            print(f"  📋 提取: [{event_type}] {fact}")

        return extracted_memories
    
    def store_memories(self, extracted_memories: List[ExtractedMemory], 
                      *, user_id: Optional[str] = None, agent_id: Optional[str] = None, 
                      run_id: Optional[str] = None, base_metadata: Optional[Dict[str, Any]] = None) -> Dict:
        """
        存储已分类的记忆
        
        Args:
            extracted_memories: 提取并分类的记忆列表
            user_id: 用户ID
            agent_id: 代理ID
            run_id: 运行ID
            base_metadata: 基础元数据
            
        Returns:
            Dict: 存储结果
        """
        print(f"💾 开始存储 {len(extracted_memories)} 条分类记忆...")
        
        # 构建过滤器和元数据
        from mem0.memory.main import _build_filters_and_metadata
        processed_metadata, effective_filters = _build_filters_and_metadata(
            user_id=user_id,
            agent_id=agent_id,
            run_id=run_id,
            input_metadata=base_metadata or {}
        )
        
        stored_results = []
        
        for i, memory in enumerate(extracted_memories, 1):
            try:
                # 合并元数据
                final_metadata = deepcopy(processed_metadata)
                final_metadata.update(memory.metadata)

                # 根据event类型处理
                if memory.event == "ADD":
                    # 创建新记忆
                    memory_id = self._create_memory_direct(
                        data=memory.text,
                        embeddings=memory.embeddings,
                        metadata=final_metadata
                    )
                    print(f"  {i}. ✅ [ADD] [{memory.category}] {memory.text}")

                elif memory.event == "UPDATE":
                    # 更新现有记忆
                    memory_id = self._update_memory_direct(
                        data=memory.text,
                        embeddings=memory.embeddings,
                        metadata=final_metadata,
                        old_memory=memory.old_memory
                    )
                    print(f"  {i}. ✅ [UPDATE] [{memory.category}] {memory.text}")

                elif memory.event == "DELETE":
                    # 删除记忆（这种情况下不需要存储）
                    print(f"  {i}. ✅ [DELETE] [{memory.category}] {memory.text}")
                    continue

                elif memory.event == "NONE":
                    # 无操作
                    print(f"  {i}. ✅ [NONE] [{memory.category}] {memory.text}")
                    continue

                else:
                    # 默认作为ADD处理
                    memory_id = self._create_memory_direct(
                        data=memory.text,
                        embeddings=memory.embeddings,
                        metadata=final_metadata
                    )
                    print(f"  {i}. ✅ [DEFAULT] [{memory.category}] {memory.text}")

                result = {
                    "id": memory_id,
                    "memory": memory.text,
                    "event": memory.event,
                    "category": memory.category
                }

                if memory.old_memory:
                    result["old_memory"] = memory.old_memory

                stored_results.append(result)

            except Exception as e:
                logger.error(f"Error storing memory {memory.text}: {e}")
                print(f"  {i}. ❌ 存储失败: [{memory.event}] {memory.text}")
        
        print(f"✅ 成功存储 {len(stored_results)} 条记忆")
        
        return {"results": stored_results}
    
    def _create_memory_direct(self, data: str, embeddings: List[float], metadata: Dict[str, Any]) -> str:
        """直接创建记忆，不重新计算嵌入向量"""
        memory_id = str(uuid.uuid4())
        
        # 准备元数据
        final_metadata = metadata.copy()
        final_metadata["data"] = data
        final_metadata["hash"] = hashlib.md5(data.encode()).hexdigest()
        final_metadata["created_at"] = datetime.datetime.now(pytz.timezone("US/Pacific")).isoformat()
        
        # 存储到向量数据库
        self.vector_store.insert(
            vectors=[embeddings],
            ids=[memory_id],
            payloads=[final_metadata],
        )
        
        # 添加历史记录
        self.db.add_history(
            memory_id,
            None,
            data,
            "ADD",
            created_at=final_metadata.get("created_at"),
            actor_id=final_metadata.get("actor_id"),
            role=final_metadata.get("role"),
        )
        
        return memory_id

    def _update_memory_direct(self, data: str, embeddings: List[float], metadata: Dict[str, Any], old_memory: str = None) -> str:
        """直接更新记忆，不重新计算嵌入向量"""
        memory_id = str(uuid.uuid4())

        # 准备元数据
        final_metadata = metadata.copy()
        final_metadata["data"] = data
        final_metadata["hash"] = hashlib.md5(data.encode()).hexdigest()
        final_metadata["updated_at"] = datetime.datetime.now(pytz.timezone("US/Pacific")).isoformat()

        # 存储到向量数据库
        self.vector_store.insert(
            vectors=[embeddings],
            ids=[memory_id],
            payloads=[final_metadata],
        )

        # 添加历史记录
        self.db.add_history(
            memory_id,
            old_memory,
            data,
            "UPDATE",
            created_at=final_metadata.get("updated_at"),
            actor_id=final_metadata.get("actor_id"),
            role=final_metadata.get("role"),
        )

        return memory_id
    
    def extract_and_classify_memories(self, messages, classifier_func, 
                                    *, user_id: Optional[str] = None, 
                                    agent_id: Optional[str] = None, 
                                    run_id: Optional[str] = None,
                                    base_metadata: Optional[Dict[str, Any]] = None) -> Dict:
        """
        一站式提取、分类和存储记忆
        
        Args:
            messages: 对话消息
            classifier_func: 分类函数，接收记忆文本，返回类别和元数据
            user_id: 用户ID
            agent_id: 代理ID
            run_id: 运行ID
            base_metadata: 基础元数据
            
        Returns:
            Dict: 处理结果
        """
        print(f"\n🔄 一站式记忆处理流程")
        
        # 第一步：提取记忆
        extracted_memories = self.extract_memories(
            messages, 
            user_id=user_id, 
            agent_id=agent_id, 
            run_id=run_id
        )
        
        if not extracted_memories:
            return {"results": [], "message": "No memories extracted"}
        
        # 第二步：分类记忆
        print(f"\n🏷️ 开始分类记忆...")
        classified_memories = []
        
        for i, memory in enumerate(extracted_memories, 1):
            try:
                # 调用分类函数
                category, metadata = classifier_func(memory.text)
                
                # 设置分类和元数据
                memory.set_category(category)
                memory.set_metadata(metadata or {})
                
                classified_memories.append(memory)
                print(f"  {i}. '{memory.text}' -> {category}")
                
            except Exception as e:
                logger.error(f"Error classifying memory '{memory.text}': {e}")
                print(f"  {i}. ❌ 分类失败: {memory.text}")
        
        # 第三步：存储记忆
        if classified_memories:
            result = self.store_memories(
                classified_memories,
                user_id=user_id,
                agent_id=agent_id, 
                run_id=run_id,
                base_metadata=base_metadata
            )
            
            return {
                "success": True,
                "extracted_count": len(extracted_memories),
                "classified_count": len(classified_memories),
                "stored_count": len(result.get("results", [])),
                "results": result.get("results", [])
            }
        else:
            return {
                "success": False,
                "message": "No memories were successfully classified",
                "extracted_count": len(extracted_memories)
            }

def create_classifier_function(categories_config: Dict[str, Dict[str, str]],
                             default_category: str = "general",
                             llm_config: Optional[Dict] = None):
    """
    创建基于LLM的智能分类函数

    Args:
        categories_config: 类别配置字典，格式为:
            {
                "category_name": {
                    "description": "类别描述",
                    "examples": "示例说明"
                }
            }
        default_category: 默认类别
        llm_config: LLM配置信息

    Returns:
        分类函数
    """
    # 初始化LLM
    llm = None
    if llm_config:
        try:
            from mem0.llms.ollama import OllamaLLM
            from mem0.configs.llms.base import BaseLlmConfig

            # 创建BaseLlmConfig对象
            config_obj = BaseLlmConfig(
                model=llm_config.get("model", "qwen3:8b"),
                temperature=llm_config.get("temperature", 0.1),
                ollama_base_url=llm_config.get("ollama_base_url", "http://localhost:11434")
            )

            llm = OllamaLLM(config_obj)
        except Exception as e:
            logger.error(f"Failed to initialize LLM for classification: {e}")
            raise Exception(f"LLM initialization required for classification: {e}")
    else:
        raise Exception("LLM configuration is required for classification")

    def classify_memory(memory_text: str) -> tuple:
        """
        使用LLM进行记忆文本分类

        Args:
            memory_text: 记忆文本

        Returns:
            tuple: (category, metadata)
        """
        return _classify_with_llm(memory_text, categories_config, llm, default_category)

    return classify_memory

def _classify_with_llm(memory_text: str, categories_config: Dict[str, Dict[str, str]],
                      llm, default_category: str) -> tuple:
    """
    使用LLM进行分类
    """
    # 构建分类规则描述
    category_descriptions = []
    allowed_categories = list(categories_config.keys())

    for category, config in categories_config.items():
        description = config.get("description", "")
        examples = config.get("examples", "")

        category_info = f"- **{category}**: {description}"
        if examples:
            category_info += f"\n  示例: {examples}"

        category_descriptions.append(category_info)

    # 构建分类prompt
    classification_prompt = f"""你是一个专业的记忆分类助手。请将给定的记忆文本分类到最合适的类别中。

记忆文本: "{memory_text}"

可选类别及其详细说明:
{chr(10).join(category_descriptions)}

分类规则:
1. 仔细分析记忆文本的主要内容和语义
2. 根据类别描述和示例，选择最符合文本内容的类别
3. 如果文本包含多种类型信息，选择最主要、最核心的类别
4. 如果无法确定具体类别，选择 "{default_category}"
5. 分类时要考虑文本的深层含义，不仅仅是表面关键词

请返回JSON格式的结果，包含以下字段:
- category: 选择的类别名称（必须是上述类别之一）
- confidence: 分类置信度 (0.0-1.0，1.0表示非常确定)
- reasoning: 分类理由 (详细说明为什么选择这个类别)

示例输出:
{{"category": "personal_info", "confidence": 0.9, "reasoning": "文本包含个人姓名和职业信息，属于个人基本信息类别"}}

请确保category字段的值必须是上述可选类别之一。"""

    try:
        # 调用LLM进行分类
        response = llm.generate_response(
            messages=[{"role": "user", "content": classification_prompt}],
            response_format={"type": "json_object"}
        )

        # 解析LLM响应
        import json
        result = json.loads(response)

        category = result.get("category", default_category)
        confidence = float(result.get("confidence", 0.5))
        reasoning = result.get("reasoning", "LLM分类")

        # 验证类别是否在允许列表中
        if category not in allowed_categories:
            logger.warning(f"LLM returned invalid category '{category}', using default")
            category = default_category
            confidence = 0.3
            reasoning = f"LLM返回无效类别'{category}'，使用默认类别"

        # 构建元数据
        metadata = {
            "classification_confidence": confidence,
            "classification_method": "llm_classification",
            "classification_reasoning": reasoning,
            "llm_response": response[:200] + "..." if len(response) > 200 else response,
            "available_categories": ",".join(allowed_categories)
        }

        return category, metadata

    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse LLM response as JSON: {e}")
        logger.error(f"LLM response was: {response}")
        raise Exception(f"LLM response parsing failed: {e}")
    except Exception as e:
        logger.error(f"LLM classification error: {e}")
        raise e



# 使用示例
if __name__ == "__main__":
    # 配置
    config = {
        "vector_store": {
            "provider": "chroma",
            "config": {
                "collection_name": "enhanced_memory_test",
                "path": "./db/enhanced_memory_db"
            }
        },
        "llm": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:8b",
                "temperature": 0.1,
                "ollama_base_url": "http://************:8088"
            }
        },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:8b",
                "ollama_base_url": "http://************:8088"
            }
        }
    }
    
    # 创建增强的Memory实例
    enhanced_memory = EnhancedMemory.from_config(config)
    
    # 定义分类配置
    categories_config = {
        "personal_info": {
            "description": "个人基本信息，包括姓名、年龄、职业、住址等",
            "examples": "我叫张三、今年25岁、是一名工程师"
        },
        "preferences": {
            "description": "个人偏好和喜好，包括饮食偏好、娱乐爱好等",
            "examples": "喜欢喝咖啡、不喜欢辣食、爱听音乐"
        },
        "work_tasks": {
            "description": "工作相关的任务、项目、会议、截止日期等",
            "examples": "项目deadline、明天开会、完成开发"
        },
        "technical_knowledge": {
            "description": "技术知识和技能，包括编程语言、框架、工具等",
            "examples": "学习Python、掌握React、了解算法"
        },
        "general": {
            "description": "通用类别，用于无法明确分类的信息",
            "examples": "天气很好、心情不错、随机想法"
        }
    }

    # 创建LLM分类函数
    llm_config = config["llm"]["config"]
    classifier = create_classifier_function(categories_config, default_category="general", llm_config=llm_config)
    
    # 测试对话
    test_messages = [
        {"role": "user", "content": "我叫张三，是一名Python工程师，喜欢喝咖啡"},
        {"role": "assistant", "content": "很高兴认识你张三"}
    ]
    
    print("=" * 60)
    print("🧠 增强Memory类测试")
    print("=" * 60)
    
    # 方式一：分步操作
    print("\n📋 方式一：分步操作")
    
    # 1. 提取记忆
    extracted = enhanced_memory.extract_memories(test_messages, user_id="test_user")
    
    # 2. 手动分类
    for memory in extracted:
        category, metadata = classifier(memory.text)
        memory.set_category(category).set_metadata(metadata)
    
    # 3. 存储记忆
    result = enhanced_memory.store_memories(extracted, user_id="test_user")
    
    # 方式二：一站式操作
    print("\n📋 方式二：一站式操作")
    result2 = enhanced_memory.extract_and_classify_memories(
        test_messages, 
        classifier, 
        user_id="test_user2"
    )
    
    print(f"\n✅ 测试完成！")
    print(f"分步操作结果: {len(result.get('results', []))} 条记忆")
    print(f"一站式操作结果: {result2.get('stored_count', 0)} 条记忆")
