"""
分离接口Demo
展示如何使用分离的extract_memories()和store_memories()接口
避免重复调用add和delete，提供更清晰的记忆管理流程
"""

import sys
import os
import time
import datetime
from typing import Dict, List, Any, Optional
from enum import Enum

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

import logging
from enhanced_memory import EnhancedMemory, create_classifier_function

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MemoryCategory(Enum):
    """记忆类别枚举"""
    PERSONAL_INFO = "personal_info"
    PREFERENCES = "preferences"
    WORK_TASKS = "work_tasks"
    TECHNICAL_KNOWLEDGE = "technical_knowledge"
    LEARNING = "learning"
    HEALTH = "health"
    FINANCE = "finance"
    GENERAL = "general"

class SeparatedInterfaceDemo:
    """分离接口演示类"""
    
    def __init__(self, allowed_categories: List[MemoryCategory] = None):
        """初始化演示类"""
        
        # 设置允许的类别
        if allowed_categories is None:
            self.allowed_categories = [
                MemoryCategory.PERSONAL_INFO,
                MemoryCategory.PREFERENCES,
                MemoryCategory.WORK_TASKS,
                MemoryCategory.TECHNICAL_KNOWLEDGE,
                MemoryCategory.LEARNING,
                MemoryCategory.GENERAL
            ]
        else:
            self.allowed_categories = allowed_categories
        
        print(f"📋 支持的记忆类别:")
        for i, category in enumerate(self.allowed_categories, 1):
            print(f"  {i}. {category.value}")
        
        # 初始化增强的Memory
        config = {
            "vector_store": {
                "provider": "chroma",
                "config": {
                    "collection_name": "separated_interface",
                    "path": "./db/separated_interface_db"
                }
            },
            "llm": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "temperature": 0.1,
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            },
            "embedder": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            }
        }
        
        self.enhanced_memory = EnhancedMemory.from_config(config)
        self.user_id = "separated_demo_user"
        
        # 定义分类关键词
        self.category_keywords = {
            MemoryCategory.PERSONAL_INFO.value: ["姓名", "年龄", "职业", "住址", "家庭", "身份"],
            MemoryCategory.PREFERENCES.value: ["喜欢", "不喜欢", "偏好", "爱好", "倾向", "习惯"],
            MemoryCategory.WORK_TASKS.value: ["工作", "任务", "项目", "deadline", "会议", "同事"],
            MemoryCategory.TECHNICAL_KNOWLEDGE.value: ["技术", "编程", "代码", "框架", "算法", "工具"],
            MemoryCategory.LEARNING.value: ["学习", "课程", "技能", "知识", "培训", "教育"],
            MemoryCategory.HEALTH.value: ["健康", "医生", "药物", "锻炼", "饮食"],
            MemoryCategory.FINANCE.value: ["金钱", "投资", "理财", "银行", "收入"],
            MemoryCategory.GENERAL.value: []
        }
        
        # 创建LLM分类函数
        allowed_category_names = [cat.value for cat in self.allowed_categories]
        self.classifier = create_classifier_function(
            self.category_keywords,
            allowed_category_names,
            MemoryCategory.GENERAL.value,
            llm_config=config["llm"]["config"]  # 使用LLM分类
        )
    
    def demo_separated_interface(self, conversation: List[Dict]):
        """演示分离接口的使用"""
        print(f"\n" + "=" * 60)
        print("🔄 分离接口演示")
        print("=" * 60)
        
        print(f"\n📝 输入对话:")
        for i, msg in enumerate(conversation, 1):
            print(f"  {i}. {msg['role']}: {msg['content']}")
        
        # 第一步：提取记忆内容
        print(f"\n🧠 第一步：提取记忆内容")
        print("-" * 40)
        
        extracted_memories = self.enhanced_memory.extract_memories(
            conversation, 
            user_id=self.user_id
        )
        
        if not extracted_memories:
            print("❌ 未提取到任何记忆")
            return
        
        print(f"✅ 成功提取 {len(extracted_memories)} 条记忆:")
        for i, memory in enumerate(extracted_memories, 1):
            print(f"  {i}. [{memory.event}] {memory.text}")
            if memory.old_memory:
                print(f"      📝 更新前: {memory.old_memory}")
        
        # 第二步：外部分类处理
        print(f"\n🏷️ 第二步：外部分类处理")
        print("-" * 40)
        
        classified_memories = []
        for i, memory in enumerate(extracted_memories, 1):
            # 调用分类函数
            category, metadata = self.classifier(memory.text)
            
            # 检查类别是否允许
            if category not in [cat.value for cat in self.allowed_categories]:
                print(f"  {i}. ❌ 跳过不允许的类别: {category}")
                continue
            
            # 设置分类和元数据
            memory.set_category(category)
            
            # 添加额外的元数据
            additional_metadata = {
                "importance": "high" if category == MemoryCategory.PERSONAL_INFO.value else "medium",
                "processed_at": datetime.datetime.now().isoformat(),
                "demo_source": "separated_interface"
            }
            memory.set_metadata({**metadata, **additional_metadata})
            
            classified_memories.append(memory)
            confidence = metadata.get("classification_confidence", 0)
            print(f"  {i}. ✅ [{memory.event}] '{memory.text}' -> {category} (置信度: {confidence:.2f})")
        
        # 第三步：存储分类后的记忆
        print(f"\n💾 第三步：存储分类后的记忆")
        print("-" * 40)
        
        if classified_memories:
            base_metadata = {
                "session_id": f"demo_{int(time.time())}",
                "processing_method": "separated_interface"
            }
            
            result = self.enhanced_memory.store_memories(
                classified_memories,
                user_id=self.user_id,
                base_metadata=base_metadata
            )
            
            print(f"✅ 成功存储 {len(result.get('results', []))} 条记忆")
            
            return {
                "extracted_count": len(extracted_memories),
                "classified_count": len(classified_memories),
                "stored_count": len(result.get('results', [])),
                "results": result.get('results', [])
            }
        else:
            print("❌ 没有记忆通过分类验证")
            return {"extracted_count": len(extracted_memories), "stored_count": 0}
    
    def demo_one_step_interface(self, conversation: List[Dict]):
        """演示一站式接口的使用"""
        print(f"\n" + "=" * 60)
        print("⚡ 一站式接口演示")
        print("=" * 60)
        
        print(f"\n📝 输入对话:")
        for i, msg in enumerate(conversation, 1):
            print(f"  {i}. {msg['role']}: {msg['content']}")
        
        # 一站式处理
        result = self.enhanced_memory.extract_and_classify_memories(
            conversation,
            self.classifier,
            user_id=f"{self.user_id}_onestep",
            base_metadata={
                "session_id": f"onestep_{int(time.time())}",
                "processing_method": "one_step_interface"
            }
        )
        
        return result
    
    def compare_with_original_method(self, conversation: List[Dict]):
        """与原始方法对比"""
        print(f"\n" + "=" * 60)
        print("🆚 与原始方法对比")
        print("=" * 60)
        
        print(f"\n📝 输入对话:")
        for i, msg in enumerate(conversation, 1):
            print(f"  {i}. {msg['role']}: {msg['content']}")
        
        print(f"\n❌ 原始方法的问题:")
        print("  1. 需要先调用 memory.add(infer=True) 提取记忆")
        print("  2. 然后调用 memory.delete_all() 删除临时记忆")
        print("  3. 再次调用 memory.add(infer=False) 存储分类记忆")
        print("  4. 可能存储额外的无关信息")
        print("  5. 重复的网络调用和计算开销")
        
        print(f"\n✅ 新方法的优势:")
        print("  1. extract_memories() 只提取，不存储")
        print("  2. 外部进行分类和元数据处理")
        print("  3. store_memories() 直接存储最终结果")
        print("  4. 避免重复调用和临时存储")
        print("  5. 更清晰的职责分离")
    
    def search_by_category(self, query: str, categories: List[MemoryCategory] = None):
        """按类别搜索记忆"""
        print(f"\n🔍 搜索记忆: '{query}'")
        if categories:
            print(f"   限制类别: {[c.value for c in categories]}")
        
        # 使用原始的search方法
        all_results = self.enhanced_memory.search(query, user_id=self.user_id)
        
        # 过滤结果
        filtered_results = []
        for result in all_results.get("results", []):
            metadata = result.get('metadata', {})
            result_category = metadata.get('category', 'unknown')
            
            # 检查类别过滤
            if categories:
                if not any(c.value == result_category for c in categories):
                    continue
            
            result['category'] = result_category
            result['importance'] = metadata.get('importance', 'unknown')
            result['processing_method'] = metadata.get('processing_method', 'unknown')
            filtered_results.append(result)
        
        print(f"📋 搜索结果 ({len(filtered_results)} 条):")
        for i, result in enumerate(filtered_results, 1):
            category = result.get('category', 'unknown')
            importance = result.get('importance', 'unknown')
            method = result.get('processing_method', 'unknown')
            score = result.get('score', 0)
            print(f"  {i}. [{category}] {result['memory']} (重要性:{importance}, 方法:{method}, 相关性:{score:.2f})")
        
        return {"results": filtered_results}

def main():
    """主演示函数"""
    print("=" * 80)
    print("🧠 分离接口记忆管理Demo")
    print("=" * 80)
    
    # 初始化演示
    demo = SeparatedInterfaceDemo()
    
    # 清理之前的数据
    print("\n🗑️ 清理之前的测试数据...")
    try:
        demo.enhanced_memory.delete_all(user_id=demo.user_id)
        demo.enhanced_memory.delete_all(user_id=f"{demo.user_id}_onestep")
        print("✅ 清理完成")
    except:
        print("✅ 无需清理")
    
    # 测试对话
    test_conversation = [
        {"role": "user", "content": "你好，我是王五，今年30岁，是一名机器学习工程师"},
        {"role": "assistant", "content": "你好王五！很高兴认识你"},
        {"role": "user", "content": "我最近在学习深度学习，特别是Transformer架构"},
        {"role": "assistant", "content": "Transformer是很重要的架构，在NLP领域应用广泛"},
        {"role": "user", "content": "我喜欢喝绿茶，平时喜欢跑步锻炼身体"},
        {"role": "assistant", "content": "很好的生活习惯！"}
    ]
    
    # 1. 演示分离接口
    result1 = demo.demo_separated_interface(test_conversation)
    
    # 2. 演示一站式接口
    result2 = demo.demo_one_step_interface(test_conversation)
    
    # 3. 对比原始方法
    demo.compare_with_original_method(test_conversation)
    
    # 4. 搜索测试
    print(f"\n" + "=" * 60)
    print("🔍 搜索测试")
    print("=" * 60)
    
    # 搜索个人信息
    demo.search_by_category("王五", [MemoryCategory.PERSONAL_INFO])
    
    # 搜索学习相关
    demo.search_by_category("学习", [MemoryCategory.LEARNING, MemoryCategory.TECHNICAL_KNOWLEDGE])
    
    # 全局搜索
    demo.search_by_category("机器学习")
    
    # 5. 结果对比
    print(f"\n" + "=" * 60)
    print("📊 结果对比")
    print("=" * 60)
    
    if result1 and result2:
        print(f"分离接口结果:")
        print(f"  - 提取记忆: {result1.get('extracted_count', 0)} 条")
        print(f"  - 分类记忆: {result1.get('classified_count', 0)} 条")
        print(f"  - 存储记忆: {result1.get('stored_count', 0)} 条")
        
        print(f"\n一站式接口结果:")
        print(f"  - 提取记忆: {result2.get('extracted_count', 0)} 条")
        print(f"  - 分类记忆: {result2.get('classified_count', 0)} 条")
        print(f"  - 存储记忆: {result2.get('stored_count', 0)} 条")
    
    print(f"\n" + "=" * 80)
    print("🎉 分离接口Demo完成！")
    print("📝 新接口的优势:")
    print("  ✅ extract_memories() - 纯粹的记忆提取，不存储")
    print("  ✅ store_memories() - 纯粹的记忆存储，不重复计算")
    print("  ✅ 避免了重复的add/delete调用")
    print("  ✅ 更清晰的职责分离")
    print("  ✅ 支持外部分类和元数据处理")
    print("  ✅ 减少网络开销和计算成本")
    print("=" * 80)

if __name__ == "__main__":
    main()
