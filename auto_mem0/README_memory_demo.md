# 短期记忆和长期记忆Demo

这个项目演示了如何使用mem0实现不同类型的记忆管理系统，包括短期记忆、长期记忆以及更高级的记忆分类和管理功能。

## 文件说明

### 1. `memory_types_demo.py` - 基础记忆类型演示

这个文件演示了基本的短期记忆和长期记忆的区别和使用方法。

**主要功能：**
- 短期记忆：使用内存存储，会话结束后消失
- 长期记忆：使用持久化存储，永久保存
- 记忆搜索和检索
- 记忆过期模拟
- 记忆清理功能

**运行方式：**
```bash
cd auto_mem0
python memory_types_demo.py
```

### 2. `advanced_memory_demo.py` - 高级记忆管理演示

这个文件演示了更复杂的记忆管理系统，包括多种记忆类型、优先级管理、分类标签等。

**记忆类型：**
- `SHORT_TERM`: 短期记忆（1小时过期）
- `WORKING`: 工作记忆（8小时过期）
- `LONG_TERM`: 长期记忆（永久保存）
- `EPISODIC`: 情节记忆（特定事件）
- `SEMANTIC`: 语义记忆（知识和概念）

**功能特点：**
- 优先级管理（LOW, MEDIUM, HIGH, CRITICAL）
- 分类和标签系统
- 自定义过期时间
- 智能搜索和过滤
- 过期记忆自动清理
- 记忆统计和监控

**运行方式：**
```bash
cd auto_mem0
python advanced_memory_demo.py
```

## 核心概念

### 短期记忆 vs 长期记忆

| 特性     | 短期记忆       | 长期记忆         |
| -------- | -------------- | ---------------- |
| 存储方式 | 内存或临时文件 | 持久化数据库     |
| 生命周期 | 会话级别       | 永久保存         |
| 用途     | 当前对话上下文 | 用户偏好、知识库 |
| 过期时间 | 通常1-8小时    | 无过期或很长时间 |
| 优先级   | 中低优先级     | 高优先级         |

### 记忆类型详解

1. **短期记忆 (Short-term Memory)**
   - 存储当前会话的临时信息
   - 自动过期清理
   - 适用于：当前任务状态、临时提醒

2. **工作记忆 (Working Memory)**
   - 存储任务相关的信息
   - 中等时长保存（几小时到一天）
   - 适用于：项目进度、当前工作内容

3. **长期记忆 (Long-term Memory)**
   - 永久存储重要信息
   - 不会自动过期
   - 适用于：用户偏好、基本信息、重要知识

4. **情节记忆 (Episodic Memory)**
   - 存储特定事件和经历
   - 带有时间和上下文信息
   - 适用于：会议记录、重要事件

5. **语义记忆 (Semantic Memory)**
   - 存储概念和知识
   - 结构化的信息
   - 适用于：技术知识、定义、规则

## 配置说明

### 基础配置

```python
config = {
    "vector_store": {
        "provider": "chroma",
        "config": {
            "collection_name": "memories",
            "path": "./chroma_db"  # 持久化存储
            # "path": ":memory:"   # 内存存储
        }
    },
    "llm": {
        "provider": "ollama",
        "config": {
            "model": "qwen3:8b",
            "temperature": 0.1,
            "ollama_base_url": "http://10.151.5.243:8088"
        }
    },
    "embedder": {
        "provider": "ollama",
        "config": {
            "model": "qwen3:8b",
            "ollama_base_url": "http://10.151.5.243:8088"
        }
    }
}
```

### 记忆元数据结构

```python
metadata = {
    "memory_type": "short_term",           # 记忆类型
    "priority": 3,                         # 优先级 (1-4)
    "category": "current_task",            # 分类
    "tags": ["debug", "network"],          # 标签
    "created_at": "2024-01-01T10:00:00",  # 创建时间
    "expiration_time": "2024-01-01T18:00:00"  # 过期时间
}
```

## 使用示例

### 基础使用

```python
from memory_types_demo import MemoryTypesDemo

demo = MemoryTypesDemo()

# 添加长期记忆
demo.add_long_term_memory([
    {"role": "user", "content": "我喜欢喝咖啡"},
    {"role": "assistant", "content": "记住了你的偏好"}
])

# 添加短期记忆
demo.add_short_term_memory([
    {"role": "user", "content": "我现在在调试代码"},
    {"role": "assistant", "content": "了解你的当前状态"}
])

# 搜索记忆
demo.search_long_term_memory("用户喜欢什么？")
demo.search_short_term_memory("用户在做什么？")
```

### 高级使用

```python
from advanced_memory_demo import AdvancedMemoryManager, MemoryType, MemoryPriority

manager = AdvancedMemoryManager()

# 添加带优先级和过期时间的记忆
manager.add_memory(
    [{"role": "user", "content": "重要会议提醒"}],
    MemoryType.SHORT_TERM,
    MemoryPriority.CRITICAL,
    "reminder",
    expiration_hours=2,
    tags=["meeting", "urgent"]
)

# 智能搜索
results = manager.search_memory(
    "会议",
    memory_types=[MemoryType.SHORT_TERM, MemoryType.EPISODIC],
    min_priority=MemoryPriority.HIGH
)
```

## 注意事项

1. **模型配置**：确保Ollama服务正在运行，并且模型地址正确
2. **存储路径**：长期记忆会创建持久化文件，注意磁盘空间
3. **过期清理**：在生产环境中需要实现自动的过期记忆清理机制
4. **性能优化**：大量记忆时考虑索引和分片策略

### 3. `simple_memory_types_demo.py` - 简化版记忆类型演示

这个文件提供了一个更简洁的记忆管理演示，避免了复杂的元数据配置问题。

**记忆类型：**
- `短期记忆`: 临时信息，会话级别（内存存储）
- `工作记忆`: 任务相关，项目周期内保留（临时文件存储）
- `长期记忆`: 重要信息，永久保存（持久化存储）

**功能特点：**
- 简化的API接口
- 跨所有记忆类型的智能搜索
- 记忆统计和监控
- 选择性记忆清理
- 分类管理

**运行方式：**
```bash
cd auto_mem0
python simple_memory_types_demo.py
```

## 运行结果展示

### 基础Demo运行结果

✅ **成功演示了以下功能：**
- 长期记忆存储用户基本信息（姓名、职业、居住地、偏好）
- 短期记忆存储当前会话信息（调试问题、解决方案）
- 智能搜索功能，能准确找到相关记忆
- 记忆清理功能，短期记忆可以清除，长期记忆保持不变

### 简化版Demo运行结果

✅ **成功演示了以下功能：**
- 三种记忆类型的分别管理
- 跨类型智能搜索，按相关性排序
- 记忆统计功能，实时显示各类型记忆数量
- 选择性清理，短期记忆清理后长期记忆保留

**实际运行数据：**
```
记忆统计:
  📋 短期记忆: 2 条
  💼 工作记忆: 3 条
  💾 长期记忆: 4 条
  📈 总计: 9 条记忆

清理短期记忆后:
  📋 短期记忆: 0 条
  💼 工作记忆: 3 条
  💾 长期记忆: 4 条
  📈 总计: 7 条记忆
```

## 扩展功能

可以进一步扩展的功能：
- 记忆重要性自动评估
- 记忆关联和图谱构建
- 多用户记忆隔离
- 记忆备份和恢复
- 记忆访问权限控制
- 记忆使用统计和分析
- 自动过期清理机制
- 记忆压缩和归档

## 故障排除

1. **连接错误**：检查Ollama服务是否启动
2. **模型错误**：确认模型名称和版本正确
3. **存储错误**：检查文件权限和磁盘空间
4. **内存不足**：调整批处理大小或使用更小的模型
5. **元数据错误**：确保metadata中的值为基本数据类型（字符串、数字、布尔值）
6. **JSON解析错误**：检查LLM返回的JSON格式是否正确
