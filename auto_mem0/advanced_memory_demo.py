"""
高级记忆管理Demo
演示如何实现带有过期时间、优先级和分类的记忆系统
"""

import sys
import os
import time
import datetime
import json
from typing import Dict, List, Any, Optional
from enum import Enum

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

import logging
from mem0 import Memory

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MemoryType(Enum):
    """记忆类型枚举"""
    SHORT_TERM = "short_term"      # 短期记忆：会话级别
    WORKING = "working"            # 工作记忆：任务级别
    LONG_TERM = "long_term"        # 长期记忆：永久存储
    EPISODIC = "episodic"          # 情节记忆：特定事件
    SEMANTIC = "semantic"          # 语义记忆：知识和概念

class MemoryPriority(Enum):
    """记忆优先级"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

class AdvancedMemoryManager:
    """高级记忆管理器"""
    
    def __init__(self):
        """初始化记忆管理器"""
        
        # 基础配置
        base_config = {
            "llm": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "temperature": 0.1,
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            },
            "embedder": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            }
        }
        
        # 为不同类型的记忆创建不同的存储
        self.memories = {}
        
        # 短期记忆 - 内存存储
        short_term_config = base_config.copy()
        short_term_config["vector_store"] = {
            "provider": "chroma",
            "config": {
                "collection_name": "short_term",
                "path": "./db/:memory:"
            }
        }
        self.memories[MemoryType.SHORT_TERM] = Memory.from_config(short_term_config)
        
        # 工作记忆 - 临时文件存储
        working_config = base_config.copy()
        working_config["vector_store"] = {
            "provider": "chroma",
            "config": {
                "collection_name": "working_memory",
                "path": "./db/temp_working_db"
            }
        }
        self.memories[MemoryType.WORKING] = Memory.from_config(working_config)
        
        # 长期记忆 - 持久化存储
        long_term_config = base_config.copy()
        long_term_config["vector_store"] = {
            "provider": "chroma",
            "config": {
                "collection_name": "long_term",
                "path": "./db/persistent_long_term_db"
            }
        }
        self.memories[MemoryType.LONG_TERM] = Memory.from_config(long_term_config)
        
        # 情节记忆 - 事件存储
        episodic_config = base_config.copy()
        episodic_config["vector_store"] = {
            "provider": "chroma",
            "config": {
                "collection_name": "episodic",
                "path": "./db/episodic_db"
            }
        }
        self.memories[MemoryType.EPISODIC] = Memory.from_config(episodic_config)
        
        # 语义记忆 - 知识存储
        semantic_config = base_config.copy()
        semantic_config["vector_store"] = {
            "provider": "chroma",
            "config": {
                "collection_name": "semantic",
                "path": "./db/semantic_db"
            }
        }
        self.memories[MemoryType.SEMANTIC] = Memory.from_config(semantic_config)
        
        self.user_id = "advanced_user"
    
    def add_memory(self, 
                   messages: List[Dict], 
                   memory_type: MemoryType,
                   priority: MemoryPriority = MemoryPriority.MEDIUM,
                   category: str = "general",
                   expiration_hours: Optional[int] = None,
                   tags: List[str] = None) -> Dict:
        """添加记忆到指定类型的存储中"""
        
        if tags is None:
            tags = []
        
        # 构建metadata
        metadata = {
            "memory_type": memory_type.value,
            "priority": priority.value,
            "category": category,
            "tags": ",".join(tags) if tags else "",  # 将标签列表转换为逗号分隔的字符串
            "created_at": datetime.datetime.now().isoformat(),
        }
        
        # 设置过期时间
        if expiration_hours:
            expiration_time = datetime.datetime.now() + datetime.timedelta(hours=expiration_hours)
            metadata["expiration_time"] = expiration_time.isoformat()
        
        # 根据记忆类型设置默认过期时间
        if memory_type == MemoryType.SHORT_TERM and not expiration_hours:
            # 短期记忆默认1小时过期
            expiration_time = datetime.datetime.now() + datetime.timedelta(hours=1)
            metadata["expiration_time"] = expiration_time.isoformat()
        elif memory_type == MemoryType.WORKING and not expiration_hours:
            # 工作记忆默认8小时过期
            expiration_time = datetime.datetime.now() + datetime.timedelta(hours=8)
            metadata["expiration_time"] = expiration_time.isoformat()
        
        print(f"\n📝 添加{memory_type.value}记忆 (优先级: {priority.name}):")
        for msg in messages:
            print(f"  {msg['role']}: {msg['content']}")
        
        memory_store = self.memories[memory_type]
        result = memory_store.add(messages, user_id=self.user_id, metadata=metadata)
        
        print(f"✅ 记忆添加成功: {len(result.get('results', []))} 条")
        return result
    
    def search_memory(self, 
                     query: str, 
                     memory_types: List[MemoryType] = None,
                     category: str = None,
                     min_priority: MemoryPriority = None) -> Dict:
        """搜索记忆"""
        
        if memory_types is None:
            memory_types = list(MemoryType)
        
        print(f"\n🔍 搜索记忆: '{query}'")
        print(f"   搜索范围: {[mt.value for mt in memory_types]}")
        
        all_results = []
        
        for memory_type in memory_types:
            memory_store = self.memories[memory_type]
            results = memory_store.search(query, user_id=self.user_id)
            
            for result in results.get("results", []):
                # 检查是否过期
                if self._is_memory_expired(result):
                    continue
                
                # 检查分类过滤
                if category and result.get('metadata', {}).get('category') != category:
                    continue
                
                # 检查优先级过滤
                if min_priority:
                    memory_priority = result.get('metadata', {}).get('priority', 1)
                    if memory_priority < min_priority.value:
                        continue
                
                result['source_memory_type'] = memory_type.value
                all_results.append(result)
        
        # 按优先级和相关性排序
        all_results.sort(key=lambda x: (
            -x.get('metadata', {}).get('priority', 1),  # 优先级降序
            -x.get('score', 0)  # 相关性降序
        ))
        
        print(f"📋 搜索结果 ({len(all_results)} 条):")
        for i, result in enumerate(all_results[:5], 1):  # 只显示前5条
            memory_type = result.get('source_memory_type', 'unknown')
            priority = result.get('metadata', {}).get('priority', 1)
            score = result.get('score', 0)
            print(f"  {i}. [{memory_type}] {result['memory']} (优先级:{priority}, 相关性:{score:.2f})")
        
        return {"results": all_results}
    
    def _is_memory_expired(self, memory_result: Dict) -> bool:
        """检查记忆是否过期"""
        metadata = memory_result.get('metadata', {})
        expiration_time_str = metadata.get('expiration_time')
        
        if not expiration_time_str:
            return False
        
        try:
            expiration_time = datetime.datetime.fromisoformat(expiration_time_str)
            return datetime.datetime.now() > expiration_time
        except:
            return False
    
    def cleanup_expired_memories(self):
        """清理过期的记忆"""
        print(f"\n🗑️ 清理过期记忆...")
        
        cleaned_count = 0
        for memory_type, memory_store in self.memories.items():
            try:
                all_memories = memory_store.get_all(user_id=self.user_id)
                for memory in all_memories.get("results", []):
                    if self._is_memory_expired(memory):
                        # 在实际应用中，这里应该删除过期的记忆
                        # memory_store.delete(memory['id'], user_id=self.user_id)
                        cleaned_count += 1
                        print(f"  🗑️ 发现过期记忆: {memory['memory'][:50]}...")
            except Exception as e:
                print(f"  ❌ 清理{memory_type.value}记忆时出错: {e}")
        
        print(f"✅ 发现 {cleaned_count} 条过期记忆（实际应用中会被删除）")
    
    def get_memory_statistics(self) -> Dict:
        """获取记忆统计信息"""
        print(f"\n📊 记忆统计信息:")
        
        stats = {}
        total_memories = 0
        
        for memory_type, memory_store in self.memories.items():
            try:
                memories = memory_store.get_all(user_id=self.user_id)
                count = len(memories.get("results", []))
                stats[memory_type.value] = count
                total_memories += count
                print(f"  {memory_type.value}: {count} 条")
            except Exception as e:
                print(f"  {memory_type.value}: 获取失败 ({e})")
                stats[memory_type.value] = 0
        
        print(f"  总计: {total_memories} 条记忆")
        return stats

def demo_advanced_memory_management():
    """演示高级记忆管理功能"""
    print("=" * 80)
    print("🧠 高级记忆管理系统Demo")
    print("=" * 80)
    
    manager = AdvancedMemoryManager()
    
    # 1. 添加不同类型的记忆
    print("\n" + "=" * 50)
    print("1️⃣ 添加不同类型的记忆")
    print("=" * 50)
    
    # 长期记忆 - 用户基本信息
    manager.add_memory(
        [{"role": "user", "content": "我是李四，软件架构师，专注于微服务和云原生技术"}],
        MemoryType.LONG_TERM,
        MemoryPriority.HIGH,
        "personal_info",
        tags=["profile", "career"]
    )
    
    # 语义记忆 - 技术知识
    manager.add_memory(
        [{"role": "user", "content": "Docker是容器化技术，Kubernetes是容器编排平台"}],
        MemoryType.SEMANTIC,
        MemoryPriority.MEDIUM,
        "technology",
        tags=["docker", "kubernetes", "containers"]
    )
    
    # 工作记忆 - 当前任务
    manager.add_memory(
        [{"role": "user", "content": "我正在设计一个新的微服务架构，需要考虑服务发现和负载均衡"}],
        MemoryType.WORKING,
        MemoryPriority.HIGH,
        "current_task",
        expiration_hours=8,
        tags=["microservices", "architecture"]
    )
    
    # 短期记忆 - 临时信息
    manager.add_memory(
        [{"role": "user", "content": "我现在在调试一个网络连接问题，端口8080无法访问"}],
        MemoryType.SHORT_TERM,
        MemoryPriority.MEDIUM,
        "debugging",
        expiration_hours=1,
        tags=["debug", "network"]
    )
    
    # 情节记忆 - 特定事件
    manager.add_memory(
        [{"role": "user", "content": "昨天的技术分享会上，我们讨论了GraphQL的优缺点"}],
        MemoryType.EPISODIC,
        MemoryPriority.LOW,
        "meeting",
        tags=["meeting", "graphql", "discussion"]
    )
    
    # 2. 搜索测试
    print("\n" + "=" * 50)
    print("2️⃣ 搜索测试")
    print("=" * 50)
    
    # 搜索技术相关信息
    manager.search_memory("微服务架构")
    
    # 搜索特定类型的记忆
    manager.search_memory("Docker", [MemoryType.SEMANTIC, MemoryType.WORKING])
    
    # 搜索高优先级记忆
    manager.search_memory("李四", min_priority=MemoryPriority.HIGH)
    
    # 3. 统计信息
    print("\n" + "=" * 50)
    print("3️⃣ 记忆统计")
    print("=" * 50)
    
    manager.get_memory_statistics()
    
    # 4. 模拟时间流逝和过期清理
    print("\n" + "=" * 50)
    print("4️⃣ 过期记忆清理")
    print("=" * 50)
    
    manager.cleanup_expired_memories()
    
    # 5. 添加更多测试数据
    print("\n" + "=" * 50)
    print("5️⃣ 添加更多测试数据")
    print("=" * 50)
    
    # 添加一些即将过期的记忆
    manager.add_memory(
        [{"role": "user", "content": "临时提醒：下午3点有个会议"}],
        MemoryType.SHORT_TERM,
        MemoryPriority.CRITICAL,
        "reminder",
        expiration_hours=0.001,  # 几乎立即过期
        tags=["reminder", "meeting"]
    )
    
    # 等待一下让记忆过期
    print("⏳ 等待记忆过期...")
    time.sleep(2)
    
    # 再次清理
    manager.cleanup_expired_memories()
    
    # 6. 最终搜索测试
    print("\n" + "=" * 50)
    print("6️⃣ 最终搜索测试")
    print("=" * 50)
    
    manager.search_memory("会议")  # 应该找到情节记忆中的会议，但不包括过期的提醒
    
    print("\n" + "=" * 80)
    print("🎉 高级记忆管理Demo完成！")
    print("📝 功能特点:")
    print("  ✅ 多种记忆类型支持")
    print("  ✅ 优先级管理")
    print("  ✅ 分类和标签")
    print("  ✅ 过期时间控制")
    print("  ✅ 智能搜索和过滤")
    print("  ✅ 统计和监控")
    print("=" * 80)

if __name__ == "__main__":
    demo_advanced_memory_management()
