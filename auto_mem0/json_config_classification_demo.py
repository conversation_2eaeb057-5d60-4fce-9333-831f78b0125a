"""
基于JSON配置的LLM分类Demo
展示如何使用JSON配置文件定义分类类别，完全基于大模型进行智能分类
"""

import sys
import os
import json
import time
import datetime
from typing import Dict, List, Any, Optional

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

import logging
from enhanced_memory import EnhancedMemory, create_classifier_function

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class JsonConfigClassificationDemo:
    """基于JSON配置的LLM分类演示类"""
    
    def __init__(self, categories_config_path: str = "memory_categories.json"):
        """初始化演示类"""
        
        print(f"📋 基于JSON配置的LLM分类演示初始化")
        
        # 加载分类配置
        self.categories_config = self.load_categories_config(categories_config_path)
        
        print(f"✅ 成功加载 {len(self.categories_config)} 个分类类别:")
        for i, (category, config) in enumerate(self.categories_config.items(), 1):
            description = config.get("description", "")[:50] + "..." if len(config.get("description", "")) > 50 else config.get("description", "")
            print(f"  {i}. {category}: {description}")
        
        # 初始化增强的Memory
        self.config = {
            "vector_store": {
                "provider": "chroma",
                "config": {
                    "collection_name": "json_config_classification",
                    "path": "./db/json_config_classification_db"
                }
            },
            "llm": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "temperature": 0.1,
                    "ollama_base_url": "http://************:8088"
                }
            },
            "embedder": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "ollama_base_url": "http://************:8088"
                }
            }
        }
        
        self.enhanced_memory = EnhancedMemory.from_config(self.config)
        self.user_id = "json_config_user"
        
        # 创建LLM分类函数
        self.llm_classifier = create_classifier_function(
            self.categories_config,
            default_category="general",
            llm_config=self.config["llm"]["config"]
        )
    
    def load_categories_config(self, config_path: str) -> Dict[str, Dict[str, str]]:
        """加载分类配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"📁 成功加载分类配置文件: {config_path}")
            return config
        except FileNotFoundError:
            print(f"❌ 配置文件未找到: {config_path}")
            raise
        except json.JSONDecodeError as e:
            print(f"❌ 配置文件JSON格式错误: {e}")
            raise
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            raise
    
    def test_single_classification(self, memory_text: str):
        """测试单个记忆的分类"""
        print(f"\n📝 测试记忆分类")
        print(f"记忆内容: '{memory_text}'")
        print("-" * 60)
        
        try:
            start_time = time.time()
            category, metadata = self.llm_classifier(memory_text)
            end_time = time.time()
            
            confidence = metadata.get("classification_confidence", 0)
            reasoning = metadata.get("classification_reasoning", "无")
            method = metadata.get("classification_method", "unknown")
            
            print(f"🤖 LLM分类结果:")
            print(f"  📂 类别: {category}")
            print(f"  🎯 置信度: {confidence:.2f}")
            print(f"  🔧 方法: {method}")
            print(f"  💭 理由: {reasoning}")
            print(f"  ⏱️ 耗时: {(end_time - start_time):.2f}秒")
            
            # 显示类别描述
            if category in self.categories_config:
                category_desc = self.categories_config[category].get("description", "")
                print(f"  📖 类别说明: {category_desc}")
            
            return category, metadata
            
        except Exception as e:
            print(f"❌ 分类失败: {e}")
            return None, {}
    
    def test_batch_classification(self):
        """测试批量分类"""
        print(f"\n" + "=" * 60)
        print("🔄 批量分类测试")
        print("=" * 60)
        
        test_memories = [
            "我叫李明，今年30岁，是一名AI研究员",
            "我喜欢喝绿茶，不太喜欢咖啡，平时爱听古典音乐",
            "下周五项目deadline，需要完成深度学习模型的部署",
            "最近在学习Transformer架构和注意力机制",
            "我正在学习日语，准备明年去日本留学",
            "每天早上跑步5公里，晚上做瑜伽放松",
            "这个月投资了一些科技股，收益还不错",
            "和大学同学聚会，聊了很多往事",
            "计划下个月去云南旅行，已经订好了机票",
            "昨晚看了一部很棒的科幻电影《星际穿越》",
            "我的目标是在35岁之前成为技术专家",
            "还记得第一次参加编程比赛获奖的激动心情",
            "今天天气很好，心情也不错"
        ]
        
        results = []
        total_time = 0
        
        for i, memory_text in enumerate(test_memories, 1):
            print(f"\n{i}. 测试记忆: '{memory_text}'")
            
            try:
                start_time = time.time()
                category, metadata = self.llm_classifier(memory_text)
                end_time = time.time()
                
                classification_time = end_time - start_time
                total_time += classification_time
                
                confidence = metadata.get("classification_confidence", 0)
                reasoning = metadata.get("classification_reasoning", "")
                
                print(f"   🤖 结果: {category} (置信度: {confidence:.2f}, 耗时: {classification_time:.2f}s)")
                print(f"   💭 理由: {reasoning}")
                
                results.append({
                    "memory": memory_text,
                    "category": category,
                    "confidence": confidence,
                    "reasoning": reasoning,
                    "time": classification_time
                })
                
            except Exception as e:
                print(f"   ❌ 分类失败: {e}")
                results.append({
                    "memory": memory_text,
                    "category": "error",
                    "confidence": 0,
                    "reasoning": str(e),
                    "time": 0
                })
        
        print(f"\n📊 批量分类统计:")
        print(f"  总测试数: {len(test_memories)}")
        print(f"  成功分类: {len([r for r in results if r['category'] != 'error'])}")
        print(f"  失败数量: {len([r for r in results if r['category'] == 'error'])}")
        print(f"  总耗时: {total_time:.2f}秒")
        print(f"  平均耗时: {total_time/len(test_memories):.2f}秒/条")
        
        return results
    
    def analyze_classification_results(self, results):
        """分析分类结果"""
        print(f"\n" + "=" * 60)
        print("📊 分类结果分析")
        print("=" * 60)
        
        # 分类分布统计
        category_counts = {}
        confidence_sum = {}
        
        for result in results:
            if result["category"] != "error":
                category = result["category"]
                category_counts[category] = category_counts.get(category, 0) + 1
                confidence_sum[category] = confidence_sum.get(category, 0) + result["confidence"]
        
        print(f"\n📂 分类分布:")
        for category, count in sorted(category_counts.items()):
            avg_confidence = confidence_sum[category] / count
            category_desc = self.categories_config.get(category, {}).get("description", "")[:30] + "..."
            print(f"  {category}: {count} 条 (平均置信度: {avg_confidence:.2f}) - {category_desc}")
        
        # 置信度分析
        confidences = [r["confidence"] for r in results if r["category"] != "error"]
        if confidences:
            avg_confidence = sum(confidences) / len(confidences)
            max_confidence = max(confidences)
            min_confidence = min(confidences)
            
            print(f"\n🎯 置信度分析:")
            print(f"  平均置信度: {avg_confidence:.2f}")
            print(f"  最高置信度: {max_confidence:.2f}")
            print(f"  最低置信度: {min_confidence:.2f}")
        
        # 显示高置信度分类示例
        high_confidence_results = [r for r in results if r["confidence"] >= 0.8 and r["category"] != "error"]
        if high_confidence_results:
            print(f"\n✅ 高置信度分类示例 (置信度 >= 0.8):")
            for result in high_confidence_results[:3]:  # 只显示前3个
                print(f"  📝 '{result['memory']}'")
                print(f"     → {result['category']} (置信度: {result['confidence']:.2f})")
                print(f"     💭 {result['reasoning']}")
    
    def demo_end_to_end_workflow(self):
        """演示端到端工作流程"""
        print(f"\n" + "=" * 60)
        print("🔄 端到端工作流程演示")
        print("=" * 60)
        
        conversation = [
            {"role": "user", "content": "我是王小明，28岁，在一家AI公司做机器学习工程师"},
            {"role": "assistant", "content": "很高兴认识你王小明"},
            {"role": "user", "content": "我最近在研究大语言模型的RLHF技术，特别是PPO算法的应用"},
            {"role": "assistant", "content": "RLHF是很前沿的技术"},
            {"role": "user", "content": "我平时喜欢喝乌龙茶，也喜欢打羽毛球健身"},
            {"role": "assistant", "content": "很好的生活习惯"},
            {"role": "user", "content": "我计划明年去新加坡参加AI会议，顺便旅游几天"},
            {"role": "assistant", "content": "新加坡是个不错的地方"}
        ]
        
        print(f"\n📝 输入对话:")
        for i, msg in enumerate(conversation, 1):
            print(f"  {i}. {msg['role']}: {msg['content']}")
        
        # 使用LLM分类的完整流程
        result = self.enhanced_memory.extract_and_classify_memories(
            conversation,
            self.llm_classifier,
            user_id=self.user_id,
            base_metadata={"demo": "json_config_classification", "session": f"demo_{int(time.time())}"}
        )
        
        print(f"\n✅ 处理结果:")
        print(f"  提取记忆: {result.get('extracted_count', 0)} 条")
        print(f"  分类记忆: {result.get('classified_count', 0)} 条")
        print(f"  存储记忆: {result.get('stored_count', 0)} 条")
        
        # 显示详细结果
        if result.get('results'):
            print(f"\n📋 详细分类结果:")
            for i, memory_result in enumerate(result['results'], 1):
                category = memory_result.get('category', 'unknown')
                memory_text = memory_result.get('memory', '')
                category_desc = self.categories_config.get(category, {}).get("description", "")[:40] + "..."
                print(f"  {i}. [{category}] {memory_text}")
                print(f"     📖 {category_desc}")
        
        return result

def main():
    """主演示函数"""
    print("=" * 80)
    print("🧠 基于JSON配置的LLM智能分类Demo")
    print("=" * 80)
    
    try:
        # 初始化演示
        demo = JsonConfigClassificationDemo()
        
        # 清理之前的数据
        print("\n🗑️ 清理之前的测试数据...")
        try:
            demo.enhanced_memory.delete_all(user_id=demo.user_id)
            print("✅ 清理完成")
        except:
            print("✅ 无需清理")
        
        # 1. 单个分类测试
        print(f"\n" + "=" * 60)
        print("1️⃣ 单个分类测试")
        print("=" * 60)
        
        demo.test_single_classification("我叫张三，是一名深度学习研究员，专门研究计算机视觉")
        demo.test_single_classification("我喜欢在周末的时候去爬山，也喜欢看悬疑小说")
        demo.test_single_classification("下个月要完成神经网络模型的优化，时间比较紧张")
        
        # 2. 批量分类测试
        results = demo.test_batch_classification()
        
        # 3. 结果分析
        demo.analyze_classification_results(results)
        
        # 4. 端到端工作流程
        demo.demo_end_to_end_workflow()
        
        print(f"\n" + "=" * 80)
        print("🎉 基于JSON配置的LLM智能分类Demo完成！")
        print("📝 演示的功能:")
        print("  ✅ JSON配置加载 - 从配置文件加载分类类别和描述")
        print("  ✅ 纯LLM分类 - 完全基于大语言模型进行智能分类")
        print("  ✅ 详细分类信息 - 提供置信度、分类理由等详细信息")
        print("  ✅ 性能统计 - 统计分类耗时和成功率")
        print("  ✅ 结果分析 - 分析分类分布和置信度")
        print("  ✅ 端到端集成 - 与记忆提取存储流程完整集成")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ Demo运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
