"""
mem0自动清理策略演示
展示如何使用mem0的自动清理功能和最佳实践
"""

import sys
import os
import time
import datetime
from typing import Dict, List, Any

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

import logging
from mem0 import Memory

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AutoCleanupDemo:
    """演示mem0的自动清理策略"""
    
    def __init__(self):
        """初始化Memory实例"""
        
        self.config = {
            "vector_store": {
                "provider": "chroma",
                "config": {
                    "collection_name": "auto_cleanup_demo",
                    "path": "./db/auto_cleanup_demo_db"
                }
            },
            "llm": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "temperature": 0.1,
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            },
            "embedder": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            }
        }
        
        self.memory = Memory.from_config(self.config)
        self.user_id = "cleanup_user"
    
    def add_memory_with_strategy(self, content: str, strategy: str = "default"):
        """根据不同策略添加记忆"""
        
        messages = [
            {"role": "user", "content": content},
            {"role": "assistant", "content": f"记住了这个信息，使用{strategy}策略"}
        ]
        
        metadata = {
            "strategy": strategy,
            "created_at": datetime.datetime.now().isoformat()
        }
        
        # 根据策略设置不同的过期时间
        expiration_date = None
        if strategy == "short_term":
            # 短期记忆：1天过期
            expiration_date = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
            metadata["expiration_type"] = "short_term"
        elif strategy == "session":
            # 会话记忆：当天过期
            expiration_date = datetime.datetime.now().strftime("%Y-%m-%d")
            metadata["expiration_type"] = "session"
        elif strategy == "temporary":
            # 临时记忆：1小时过期（设置为明天，因为mem0按天计算）
            expiration_date = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
            metadata["expiration_type"] = "temporary"
        elif strategy == "long_term":
            # 长期记忆：30天过期
            expiration_date = (datetime.datetime.now() + datetime.timedelta(days=30)).strftime("%Y-%m-%d")
            metadata["expiration_type"] = "long_term"
        # permanent策略不设置过期时间
        
        print(f"\n📝 添加记忆 (策略: {strategy})")
        print(f"   内容: {content}")
        if expiration_date:
            print(f"   过期日期: {expiration_date}")
        else:
            print(f"   过期日期: 永不过期")
        
        # 添加记忆
        if expiration_date:
            result = self.memory.add(
                messages,
                user_id=self.user_id,
                metadata=metadata,
                expiration_date=expiration_date
            )
        else:
            result = self.memory.add(
                messages,
                user_id=self.user_id,
                metadata=metadata
            )
        
        print(f"✅ 记忆添加成功: {len(result.get('results', []))} 条")
        return result
    
    def demonstrate_cleanup_strategies(self):
        """演示不同的清理策略"""
        print("\n" + "=" * 50)
        print("🧹 mem0自动清理策略演示")
        print("=" * 50)
        
        strategies = [
            ("session", "当前会话的临时信息"),
            ("short_term", "短期任务相关信息"),
            ("temporary", "临时提醒和通知"),
            ("long_term", "重要的项目信息"),
            ("permanent", "用户基本信息和偏好")
        ]
        
        for strategy, description in strategies:
            self.add_memory_with_strategy(f"{description} - {strategy}策略示例", strategy)
    
    def demonstrate_natural_language_cleanup(self):
        """演示使用自然语言进行记忆清理"""
        print("\n" + "=" * 50)
        print("🗣️ 自然语言清理演示")
        print("=" * 50)
        
        # 添加一些测试记忆
        test_memories = [
            "我喜欢吃苹果",
            "我喜欢吃香蕉", 
            "我不喜欢吃橘子",
            "我的工作是程序员"
        ]
        
        for memory in test_memories:
            self.add_memory_with_strategy(memory, "test")
        
        print("\n添加测试记忆后的状态:")
        self.get_all_memories()
        
        # 使用自然语言删除特定记忆
        print("\n使用自然语言删除食物偏好:")
        delete_messages = [
            {"role": "user", "content": "删除所有关于食物偏好的记忆"}
        ]
        
        result = self.memory.add(delete_messages, user_id=self.user_id)
        print(f"删除操作结果: {result}")
        
        print("\n删除后的记忆状态:")
        self.get_all_memories()
    
    def demonstrate_batch_cleanup(self):
        """演示批量清理功能"""
        print("\n" + "=" * 50)
        print("📦 批量清理演示")
        print("=" * 50)
        
        # 获取所有记忆
        all_memories = self.memory.get_all(user_id=self.user_id)
        memories = all_memories.get("results", [])
        
        if len(memories) > 2:
            # 选择前两个记忆进行批量删除
            memories_to_delete = [
                {"memory_id": memories[0]["id"]},
                {"memory_id": memories[1]["id"]}
            ]
            
            print(f"准备批量删除 {len(memories_to_delete)} 条记忆:")
            for i, mem_del in enumerate(memories_to_delete, 1):
                memory_id = mem_del["memory_id"]
                memory_content = next((m["memory"] for m in memories if m["id"] == memory_id), "未知")
                print(f"  {i}. {memory_content}")
            
            # 注意：这里演示批量删除的概念，实际API可能不同
            print("\n执行批量删除...")
            for mem_del in memories_to_delete:
                try:
                    self.memory.delete(memory_id=mem_del["memory_id"])
                    print(f"✅ 删除成功: {mem_del['memory_id']}")
                except Exception as e:
                    print(f"❌ 删除失败: {e}")
        else:
            print("记忆数量不足，跳过批量删除演示")
    
    def demonstrate_memory_lifecycle(self):
        """演示记忆生命周期管理"""
        print("\n" + "=" * 50)
        print("🔄 记忆生命周期管理")
        print("=" * 50)
        
        # 1. 添加不同生命周期的记忆
        lifecycle_memories = [
            ("我正在调试一个bug", "session"),
            ("项目deadline是下周五", "short_term"),
            ("我的编程语言偏好是Python", "permanent")
        ]
        
        for content, strategy in lifecycle_memories:
            self.add_memory_with_strategy(content, strategy)
        
        # 2. 展示当前状态
        print("\n当前记忆状态:")
        self.get_all_memories()
        
        # 3. 模拟时间流逝
        print("\n⏳ 模拟时间流逝...")
        time.sleep(2)
        
        # 4. 再次查看（演示过期机制）
        print("\n时间流逝后的记忆状态:")
        self.get_all_memories()
    
    def get_all_memories(self):
        """获取所有有效记忆"""
        memories = self.memory.get_all(user_id=self.user_id)
        results = memories.get("results", [])
        
        print(f"📚 当前有效记忆 ({len(results)} 条):")
        for i, mem in enumerate(results, 1):
            metadata = mem.get('metadata', {})
            strategy = metadata.get('strategy', 'unknown')
            print(f"  {i}. [{strategy}] {mem['memory']}")
        
        return memories
    
    def cleanup_all_memories(self):
        """清理所有记忆"""
        print(f"\n🗑️ 清理所有记忆...")
        try:
            result = self.memory.delete_all(user_id=self.user_id)
            print(f"✅ 所有记忆已清理: {result}")
        except Exception as e:
            print(f"❌ 清理记忆时出错: {e}")

def main():
    """主演示函数"""
    print("=" * 80)
    print("🧠 mem0自动清理策略演示")
    print("=" * 80)
    
    demo = AutoCleanupDemo()
    
    # 清理之前的数据
    demo.cleanup_all_memories()
    
    # 1. 演示不同清理策略
    demo.demonstrate_cleanup_strategies()
    
    # 2. 查看初始状态
    print("\n" + "=" * 50)
    print("📊 初始记忆状态")
    print("=" * 50)
    demo.get_all_memories()
    
    # 3. 演示自然语言清理
    demo.demonstrate_natural_language_cleanup()
    
    # 4. 演示批量清理
    demo.demonstrate_batch_cleanup()
    
    # 5. 演示记忆生命周期
    demo.demonstrate_memory_lifecycle()
    
    # 6. 最终清理
    print("\n" + "=" * 50)
    print("🧹 最终清理")
    print("=" * 50)
    
    user_input = input("是否清理所有演示记忆？(y/n): ")
    if user_input.lower() == 'y':
        demo.cleanup_all_memories()
        print("✅ 演示记忆已清理")
    
    print("\n" + "=" * 80)
    print("🎉 mem0自动清理策略演示完成！")
    print("📝 关键特性:")
    print("  ✅ 内置过期时间支持")
    print("  ✅ 多种清理策略")
    print("  ✅ 自然语言清理命令")
    print("  ✅ 批量删除功能")
    print("  ✅ 记忆生命周期管理")
    print("  ✅ 自动过滤过期记忆")
    print("=" * 80)

if __name__ == "__main__":
    main()
