#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web Demo 05 配置文件
"""

import os

class Config:
    """基础配置类"""
    
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'mem0-web-demo-secret-key'
    DEBUG = os.environ.get('DEBUG', 'True').lower() == 'true'
    
    # 服务器配置
    HOST = os.environ.get('HOST', '0.0.0.0')
    PORT = int(os.environ.get('PORT', 5000))
    
    # Mem0配置
    MEM0_CONFIG = {
        "vector_store": {
            "provider": "chroma",
            "config": {
                "collection_name": os.environ.get('COLLECTION_NAME', 'web_demo_05'),
                "path": os.environ.get('DB_PATH', './db/web_demo_05_db')
            }
        },
        "llm": {
            "provider": "ollama",
            "config": {
                "model": os.environ.get('LLM_MODEL', 'qwen3:8b'),
                "temperature": float(os.environ.get('LLM_TEMPERATURE', '0.1')),
                "ollama_base_url": os.environ.get('OLLAMA_URL', 'http://************:8088')
            }
        },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": os.environ.get('EMBEDDER_MODEL', 'qwen3:8b'),
                "ollama_base_url": os.environ.get('OLLAMA_URL', 'http://************:8088')
            }
        }
    }
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_MAX_ENTRIES = int(os.environ.get('LOG_MAX_ENTRIES', 1000))
    
    # WebSocket配置
    SOCKETIO_CORS_ALLOWED_ORIGINS = os.environ.get('CORS_ORIGINS', '*')

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'production-secret-key-change-this'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    MEM0_CONFIG = {
        "vector_store": {
            "provider": "chroma",
            "config": {
                "collection_name": "test_collection",
                "path": "./db/test_db"
            }
        },
        "llm": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:8b",
                "temperature": 0.1,
                "ollama_base_url": "http://************:8088"
            }
        },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:8b",
                "ollama_base_url": "http://************:8088"
            }
        }
    }

# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """获取当前配置"""
    env = os.environ.get('FLASK_ENV', 'default')
    return config.get(env, config['default'])
