/* CSS变量定义 */
:root {
    /* 浅色主题 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    --accent-color: #007bff;
    --accent-hover: #0056b3;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
}

/* 深色主题 */
[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --border-color: #404040;
    --accent-color: #4dabf7;
    --accent-hover: #339af0;
    --success-color: #51cf66;
    --warning-color: #ffd43b;
    --danger-color: #ff6b6b;
    --shadow: 0 2px 4px rgba(0,0,0,0.3);
    --shadow-hover: 0 4px 8px rgba(0,0,0,0.4);
}

/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: all 0.3s ease;
}

/* 应用容器 */
.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部标题栏 */
.header {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem;
    box-shadow: var(--shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.header h1 {
    color: var(--accent-color);
    font-size: 1.5rem;
    font-weight: 600;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-input {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-input label {
    font-weight: 500;
    color: var(--text-secondary);
}

.user-input input {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.connection-status {
    font-size: 0.9rem;
    font-weight: 500;
}

.status-connected {
    color: var(--success-color);
}

.status-disconnected {
    color: var(--danger-color);
}

.theme-toggle {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    background-color: var(--bg-tertiary);
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 1rem;
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    overflow: hidden;
}

/* 对话区域 */
.chat-section {
    background-color: var(--bg-primary);
    border-radius: 8px;
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h2 {
    font-size: 1.2rem;
    color: var(--text-primary);
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    scroll-behavior: smooth;
}

.welcome-message {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
}

.welcome-message i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--accent-color);
}

.message {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.message.user {
    flex-direction: row-reverse;
}

.message-content {
    max-width: 70%;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    position: relative;
}

.message.user .message-content {
    background-color: var(--accent-color);
    color: white;
    border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border-bottom-left-radius: 4px;
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.chat-input-container {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

.input-group {
    display: flex;
    gap: 0.5rem;
}

.input-group input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
}

.input-group input:focus {
    outline: none;
    border-color: var(--accent-color);
}

.input-info {
    margin-top: 0.5rem;
    text-align: right;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* 信息面板 */
.info-panel {
    background-color: var(--bg-primary);
    border-radius: 8px;
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.tab-button {
    flex: 1;
    padding: 1rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab-button.active {
    color: var(--accent-color);
    border-bottom: 2px solid var(--accent-color);
}

.tab-button:hover {
    background-color: var(--bg-tertiary);
}

.tab-content {
    flex: 1;
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.tab-content.active {
    display: flex;
}

.tab-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tab-header h3 {
    font-size: 1.1rem;
    color: var(--text-primary);
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background-color: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--accent-hover);
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background-color: var(--border-color);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-small {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* 记忆和日志列表 */
.memories-list, .logs-list {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

.memory-item, .log-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: var(--bg-secondary);
    border-radius: 6px;
    border-left: 3px solid var(--accent-color);
}

.memory-content {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.memory-meta, .log-meta {
    font-size: 0.8rem;
    color: var(--text-secondary);
    display: flex;
    justify-content: space-between;
}

.log-item.INFO {
    border-left-color: var(--accent-color);
}

.log-item.WARNING {
    border-left-color: var(--warning-color);
}

.log-item.ERROR {
    border-left-color: var(--danger-color);
}

.empty-state {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 搜索容器 */
.search-container {
    padding: 0 1rem 1rem;
}

.search-input-group {
    display: flex;
    gap: 0.5rem;
}

.search-input-group input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

/* 统计信息 */
.memory-stats, .log-stats {
    padding: 0 1rem 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* 控制按钮组 */
.memory-controls, .log-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.log-level-filter {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.8rem;
}

/* 加载指示器 */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--bg-primary);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: var(--shadow-hover);
    display: none;
    align-items: center;
    gap: 1rem;
    z-index: 1000;
}

.spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 通知容器 */
.notifications-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
}

.notification {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow-hover);
    max-width: 300px;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr auto;
    }
    
    .info-panel {
        height: 300px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .header-controls {
        width: 100%;
        justify-content: space-between;
    }
}
