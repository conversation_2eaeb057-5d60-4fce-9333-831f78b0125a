<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mem0 记忆系统 Web Demo 05</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 顶部标题栏 -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-brain"></i> Mem0 记忆系统 Web Demo 05</h1>
                <div class="header-controls">
                    <div class="user-input">
                        <label for="userId">用户ID:</label>
                        <input type="text" id="userId" value="demo_user" placeholder="输入用户ID">
                    </div>
                    <div class="connection-status">
                        <span id="connectionStatus" class="status-disconnected">
                            <i class="fas fa-circle"></i> 未连接
                        </span>
                    </div>
                    <button id="themeToggle" class="theme-toggle" title="切换主题">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 左侧对话区域 -->
            <section class="chat-section">
                <div class="chat-header">
                    <h2><i class="fas fa-comments"></i> 对话界面</h2>
                    <button id="clearChat" class="btn btn-secondary">
                        <i class="fas fa-trash"></i> 清空对话
                    </button>
                </div>
                
                <div class="chat-container">
                    <div id="chatMessages" class="chat-messages">
                        <div class="welcome-message">
                            <i class="fas fa-robot"></i>
                            <p>欢迎使用 Mem0 记忆系统！我会记住我们的对话内容。</p>
                        </div>
                    </div>
                    
                    <div class="chat-input-container">
                        <div class="input-group">
                            <input type="text" id="messageInput" placeholder="输入您的消息..." maxlength="1000">
                            <button id="sendMessage" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                        <div class="input-info">
                            <span id="charCount">0/1000</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 右侧信息面板 -->
            <aside class="info-panel">
                <div class="panel-tabs">
                    <button class="tab-button active" data-tab="memories">
                        <i class="fas fa-memory"></i> 记忆
                    </button>
                    <button class="tab-button" data-tab="logs">
                        <i class="fas fa-list-alt"></i> 日志
                    </button>
                </div>

                <!-- 记忆标签页 -->
                <div id="memoriesTab" class="tab-content active">
                    <div class="tab-header">
                        <h3>记忆管理</h3>
                        <div class="memory-controls">
                            <button id="refreshMemories" class="btn btn-small">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button id="clearMemories" class="btn btn-small btn-danger">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="search-container">
                        <div class="search-input-group">
                            <input type="text" id="memorySearch" placeholder="搜索记忆...">
                            <button id="searchMemories" class="btn btn-small">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="memory-stats">
                        <span id="memoryCount">记忆数量: 0</span>
                    </div>
                    
                    <div id="memoriesList" class="memories-list">
                        <div class="empty-state">
                            <i class="fas fa-brain"></i>
                            <p>暂无记忆数据</p>
                        </div>
                    </div>
                </div>

                <!-- 日志标签页 -->
                <div id="logsTab" class="tab-content">
                    <div class="tab-header">
                        <h3>系统日志</h3>
                        <div class="log-controls">
                            <select id="logLevel" class="log-level-filter">
                                <option value="all">所有级别</option>
                                <option value="INFO">信息</option>
                                <option value="WARNING">警告</option>
                                <option value="ERROR">错误</option>
                            </select>
                            <button id="clearLogs" class="btn btn-small">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="log-stats">
                        <span id="logCount">日志条数: 0</span>
                    </div>
                    
                    <div id="logsList" class="logs-list">
                        <div class="empty-state">
                            <i class="fas fa-list-alt"></i>
                            <p>暂无日志数据</p>
                        </div>
                    </div>
                </div>
            </aside>
        </main>
    </div>

    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="loading-indicator">
        <div class="spinner"></div>
        <span>处理中...</span>
    </div>

    <!-- 通知容器 -->
    <div id="notifications" class="notifications-container"></div>

    <!-- 脚本 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="script.js"></script>
</body>
</html>
