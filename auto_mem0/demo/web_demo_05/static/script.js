/**
 * Mem0 Web Demo 05 - 前端JavaScript
 * 处理用户交互、WebSocket连接和实时更新
 */

class Mem0WebApp {
    constructor() {
        this.socket = null;
        this.currentUserId = 'demo_user';
        this.isConnected = false;
        this.memories = [];
        this.logs = [];
        this.currentTab = 'memories';
        
        this.init();
    }
    
    init() {
        this.initializeElements();
        this.bindEvents();
        this.initializeSocket();
        this.loadTheme();
        this.refreshMemories();
    }
    
    initializeElements() {
        // 获取DOM元素
        this.elements = {
            // 用户控制
            userIdInput: document.getElementById('userId'),
            connectionStatus: document.getElementById('connectionStatus'),
            themeToggle: document.getElementById('themeToggle'),
            
            // 对话相关
            chatMessages: document.getElementById('chatMessages'),
            messageInput: document.getElementById('messageInput'),
            sendButton: document.getElementById('sendMessage'),
            clearChatButton: document.getElementById('clearChat'),
            charCount: document.getElementById('charCount'),
            
            // 标签页
            tabButtons: document.querySelectorAll('.tab-button'),
            memoriesTab: document.getElementById('memoriesTab'),
            logsTab: document.getElementById('logsTab'),
            
            // 记忆相关
            memoriesList: document.getElementById('memoriesList'),
            memoryCount: document.getElementById('memoryCount'),
            refreshMemoriesButton: document.getElementById('refreshMemories'),
            clearMemoriesButton: document.getElementById('clearMemories'),
            memorySearchInput: document.getElementById('memorySearch'),
            searchMemoriesButton: document.getElementById('searchMemories'),
            
            // 日志相关
            logsList: document.getElementById('logsList'),
            logCount: document.getElementById('logCount'),
            clearLogsButton: document.getElementById('clearLogs'),
            logLevelFilter: document.getElementById('logLevel'),
            
            // 其他
            loadingIndicator: document.getElementById('loadingIndicator'),
            notificationsContainer: document.getElementById('notifications')
        };
    }
    
    bindEvents() {
        // 用户ID变更
        this.elements.userIdInput.addEventListener('change', (e) => {
            this.currentUserId = e.target.value || 'demo_user';
            this.joinUser();
            this.refreshMemories();
        });
        
        // 主题切换
        this.elements.themeToggle.addEventListener('click', () => {
            this.toggleTheme();
        });
        
        // 发送消息
        this.elements.sendButton.addEventListener('click', () => {
            this.sendMessage();
        });
        
        this.elements.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // 字符计数
        this.elements.messageInput.addEventListener('input', (e) => {
            const count = e.target.value.length;
            this.elements.charCount.textContent = `${count}/1000`;
        });
        
        // 清空对话
        this.elements.clearChatButton.addEventListener('click', () => {
            this.clearChat();
        });
        
        // 标签页切换
        this.elements.tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });
        
        // 记忆相关事件
        this.elements.refreshMemoriesButton.addEventListener('click', () => {
            this.refreshMemories();
        });
        
        this.elements.clearMemoriesButton.addEventListener('click', () => {
            this.clearMemories();
        });
        
        this.elements.searchMemoriesButton.addEventListener('click', () => {
            this.searchMemories();
        });
        
        this.elements.memorySearchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchMemories();
            }
        });
        
        // 日志相关事件
        this.elements.clearLogsButton.addEventListener('click', () => {
            this.clearLogs();
        });
        
        this.elements.logLevelFilter.addEventListener('change', () => {
            this.filterLogs();
        });
    }
    
    initializeSocket() {
        try {
            this.socket = io();
            
            this.socket.on('connect', () => {
                this.isConnected = true;
                this.updateConnectionStatus();
                this.joinUser();
                this.showNotification('已连接到服务器', 'success');
            });
            
            this.socket.on('disconnect', () => {
                this.isConnected = false;
                this.updateConnectionStatus();
                this.showNotification('与服务器断开连接', 'error');
            });
            
            this.socket.on('memories_updated', (data) => {
                this.handleMemoriesUpdate(data);
            });
            
            this.socket.on('log_message', (logEntry) => {
                this.addLogEntry(logEntry);
            });
            
        } catch (error) {
            console.error('Socket初始化失败:', error);
            this.showNotification('连接失败', 'error');
        }
    }
    
    updateConnectionStatus() {
        const statusElement = this.elements.connectionStatus;
        if (this.isConnected) {
            statusElement.className = 'status-connected';
            statusElement.innerHTML = '<i class="fas fa-circle"></i> 已连接';
        } else {
            statusElement.className = 'status-disconnected';
            statusElement.innerHTML = '<i class="fas fa-circle"></i> 未连接';
        }
    }
    
    joinUser() {
        if (this.socket && this.isConnected) {
            this.socket.emit('join_user', { user_id: this.currentUserId });
        }
    }
    
    async sendMessage() {
        const message = this.elements.messageInput.value.trim();
        if (!message) return;
        
        // 显示用户消息
        this.addChatMessage('user', message);
        this.elements.messageInput.value = '';
        this.elements.charCount.textContent = '0/1000';
        
        // 显示加载状态
        this.showLoading(true);
        
        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: this.currentUserId,
                    message: message
                })
            });
            
            const data = await response.json();
            
            if (response.ok) {
                // 显示AI响应
                this.addChatMessage('assistant', data.response);
            } else {
                this.showNotification(`发送失败: ${data.error}`, 'error');
            }
            
        } catch (error) {
            console.error('发送消息失败:', error);
            this.showNotification('发送消息失败', 'error');
        } finally {
            this.showLoading(false);
        }
    }
    
    addChatMessage(role, content) {
        const messagesContainer = this.elements.chatMessages;
        
        // 移除欢迎消息
        const welcomeMessage = messagesContainer.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        
        messageDiv.innerHTML = `
            <div class="message-content">
                ${content}
                <div class="message-time">${timeString}</div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    clearChat() {
        const messagesContainer = this.elements.chatMessages;
        messagesContainer.innerHTML = `
            <div class="welcome-message">
                <i class="fas fa-robot"></i>
                <p>欢迎使用 Mem0 记忆系统！我会记住我们的对话内容。</p>
            </div>
        `;
    }
    
    async refreshMemories() {
        try {
            const response = await fetch(`/api/memories?user_id=${this.currentUserId}`);
            const data = await response.json();
            
            if (response.ok) {
                this.memories = data.memories || [];
                this.updateMemoriesDisplay();
                this.elements.memoryCount.textContent = `记忆数量: ${this.memories.length}`;
            } else {
                this.showNotification(`获取记忆失败: ${data.error}`, 'error');
            }
            
        } catch (error) {
            console.error('获取记忆失败:', error);
            this.showNotification('获取记忆失败', 'error');
        }
    }
    
    async searchMemories() {
        const query = this.elements.memorySearchInput.value.trim();
        if (!query) {
            this.refreshMemories();
            return;
        }
        
        try {
            const response = await fetch(`/api/memories/search?user_id=${this.currentUserId}&q=${encodeURIComponent(query)}`);
            const data = await response.json();
            
            if (response.ok) {
                this.memories = data.memories || [];
                this.updateMemoriesDisplay();
                this.elements.memoryCount.textContent = `搜索结果: ${this.memories.length}`;
            } else {
                this.showNotification(`搜索失败: ${data.error}`, 'error');
            }
            
        } catch (error) {
            console.error('搜索记忆失败:', error);
            this.showNotification('搜索记忆失败', 'error');
        }
    }
    
    async clearMemories() {
        if (!confirm('确定要清空所有记忆吗？此操作不可恢复。')) {
            return;
        }
        
        try {
            const response = await fetch(`/api/memories?user_id=${this.currentUserId}`, {
                method: 'DELETE'
            });
            
            const data = await response.json();
            
            if (response.ok) {
                this.showNotification('记忆已清空', 'success');
                this.refreshMemories();
            } else {
                this.showNotification(`清空失败: ${data.error}`, 'error');
            }
            
        } catch (error) {
            console.error('清空记忆失败:', error);
            this.showNotification('清空记忆失败', 'error');
        }
    }
    
    updateMemoriesDisplay() {
        const container = this.elements.memoriesList;
        
        if (this.memories.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-brain"></i>
                    <p>暂无记忆数据</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.memories.map(memory => `
            <div class="memory-item">
                <div class="memory-content">${memory.memory || memory.content || '无内容'}</div>
                <div class="memory-meta">
                    <span>ID: ${memory.id}</span>
                    <span>${memory.created_at ? new Date(memory.created_at).toLocaleString() : '未知时间'}</span>
                </div>
            </div>
        `).join('');
    }
    
    handleMemoriesUpdate(data) {
        this.showNotification('记忆已更新', 'success');
        this.refreshMemories();
    }
    
    addLogEntry(logEntry) {
        this.logs.unshift(logEntry);
        
        // 限制日志数量
        if (this.logs.length > 1000) {
            this.logs = this.logs.slice(0, 1000);
        }
        
        this.updateLogsDisplay();
        this.elements.logCount.textContent = `日志条数: ${this.logs.length}`;
    }
    
    updateLogsDisplay() {
        const container = this.elements.logsList;
        const levelFilter = this.elements.logLevelFilter.value;
        
        let filteredLogs = this.logs;
        if (levelFilter !== 'all') {
            filteredLogs = this.logs.filter(log => log.level === levelFilter);
        }
        
        if (filteredLogs.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-list-alt"></i>
                    <p>暂无日志数据</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = filteredLogs.map(log => `
            <div class="log-item ${log.level}">
                <div class="log-content">${log.message}</div>
                <div class="log-meta">
                    <span>${log.level}</span>
                    <span>${new Date(log.timestamp).toLocaleString()}</span>
                </div>
            </div>
        `).join('');
    }
    
    clearLogs() {
        this.logs = [];
        this.updateLogsDisplay();
        this.elements.logCount.textContent = '日志条数: 0';
    }
    
    filterLogs() {
        this.updateLogsDisplay();
    }
    
    switchTab(tabName) {
        // 更新按钮状态
        this.elements.tabButtons.forEach(button => {
            button.classList.toggle('active', button.dataset.tab === tabName);
        });
        
        // 更新内容显示
        this.elements.memoriesTab.classList.toggle('active', tabName === 'memories');
        this.elements.logsTab.classList.toggle('active', tabName === 'logs');
        
        this.currentTab = tabName;
    }
    
    showLoading(show) {
        this.elements.loadingIndicator.style.display = show ? 'flex' : 'none';
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        this.elements.notificationsContainer.appendChild(notification);
        
        // 自动移除通知
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
    
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        // 更新图标
        const icon = this.elements.themeToggle.querySelector('i');
        icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }
    
    loadTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        
        // 更新图标
        const icon = this.elements.themeToggle.querySelector('i');
        icon.className = savedTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new Mem0WebApp();
});
