@echo off
REM Mem0 Web Demo 05 启动脚本 (Windows)

echo === Mem0 Web Demo 05 启动脚本 ===
echo.

REM 检查Python版本
echo 检查Python版本...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查是否在正确的目录
if not exist "app.py" (
    echo 错误: 请在web_demo_05目录下运行此脚本
    pause
    exit /b 1
)

REM 创建数据库目录
echo 创建数据库目录...
if not exist "db" mkdir db

REM 检查依赖是否安装
echo 检查依赖...
python -c "import flask, flask_socketio, flask_cors" >nul 2>&1
if errorlevel 1 (
    echo 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

REM 启动应用
echo 启动Mem0 Web Demo 05...
echo 访问地址: http://localhost:5000
echo 按 Ctrl+C 停止服务
echo.

python app.py

pause
