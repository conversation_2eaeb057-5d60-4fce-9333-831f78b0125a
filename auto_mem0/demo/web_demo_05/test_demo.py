#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web Demo 05的功能
"""

import requests
import json
import time

def test_web_demo():
    """测试Web Demo的基本功能"""
    base_url = "http://localhost:5000"
    
    print("=== 测试Mem0 Web Demo 05 ===\n")
    
    # 1. 测试主页
    print("1. 测试主页访问...")
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            print("✓ 主页访问成功")
        else:
            print(f"✗ 主页访问失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 主页访问失败: {e}")
    
    # 2. 测试对话API
    print("\n2. 测试对话API...")
    try:
        chat_data = {
            "user_id": "test_user",
            "message": "你好，我是测试用户"
        }
        response = requests.post(f"{base_url}/api/chat", json=chat_data)
        if response.status_code == 200:
            result = response.json()
            print("✓ 对话API测试成功")
            print(f"  AI响应: {result.get('response', 'N/A')}")
        else:
            print(f"✗ 对话API测试失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 对话API测试失败: {e}")
    
    # 3. 测试获取记忆API
    print("\n3. 测试获取记忆API...")
    try:
        response = requests.get(f"{base_url}/api/memories?user_id=test_user")
        if response.status_code == 200:
            result = response.json()
            print("✓ 获取记忆API测试成功")
            print(f"  记忆数量: {result.get('count', 0)}")
        else:
            print(f"✗ 获取记忆API测试失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 获取记忆API测试失败: {e}")
    
    # 4. 测试搜索记忆API
    print("\n4. 测试搜索记忆API...")
    try:
        response = requests.get(f"{base_url}/api/memories/search?user_id=test_user&q=测试")
        if response.status_code == 200:
            result = response.json()
            print("✓ 搜索记忆API测试成功")
            print(f"  搜索结果数量: {result.get('count', 0)}")
        else:
            print(f"✗ 搜索记忆API测试失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 搜索记忆API测试失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_web_demo()
