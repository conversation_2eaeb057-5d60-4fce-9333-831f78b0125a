# Mem0 记忆系统 Web Demo 05

这是一个完整的Web测试页面，用于展示和测试mem0记忆系统的功能。

## 功能特性

### 🗣️ 对话界面
- 用户输入框，支持发送消息给AI
- 完整的对话历史显示
- 实时字符计数（最大1000字符）
- 发送和清空对话功能
- 响应式消息气泡设计

### 🧠 记忆展示区域
- 按分类显示当前存储的所有记忆
- 实时更新记忆内容（WebSocket推送）
- 显示记忆的详细信息（内容、ID、时间戳）
- 支持记忆搜索功能
- 记忆数量统计

### 📊 日志监控面板
- 实时显示系统日志信息
- 包括记忆操作日志、API调用日志、错误信息
- 支持日志级别过滤（信息/警告/错误）
- 日志自动滚动和数量限制
- 清空日志功能

### 🎨 界面设计
- 现代化响应式设计
- 深色/浅色主题切换
- 美观的用户界面
- 实时连接状态指示
- 通知系统

### ⚡ 技术特性
- WebSocket实时通信
- RESTful API接口
- 多用户支持
- 错误处理和重连机制
- 本地存储主题偏好

## 安装和运行

### 1. 环境要求
- Python 3.8+
- 已配置的mem0环境
- Ollama服务（用于LLM和embedder）

### 2. 安装依赖
```bash
cd auto_mem0/demo/web_demo_05
pip install -r requirements.txt
```

### 3. 确保Ollama服务运行
确保Ollama服务在 `http://************:8088` 运行，并且已安装 `qwen3:8b` 模型。

### 4. 启动应用
```bash
python app.py
```

### 5. 访问应用
打开浏览器访问：`http://localhost:5000`

## 使用说明

### 基本操作
1. **设置用户ID**：在顶部输入框中设置您的用户ID
2. **发送消息**：在对话框中输入消息并点击发送
3. **查看记忆**：在右侧"记忆"标签页查看存储的记忆
4. **监控日志**：在右侧"日志"标签页查看系统日志

### 记忆管理
- **刷新记忆**：点击刷新按钮获取最新记忆
- **搜索记忆**：在搜索框中输入关键词搜索相关记忆
- **清空记忆**：点击清空按钮删除所有记忆（需确认）

### 日志监控
- **过滤日志**：使用下拉菜单按级别过滤日志
- **清空日志**：点击清空按钮清除所有日志

### 主题切换
点击右上角的月亮/太阳图标切换深色/浅色主题。

## API接口

### 对话接口
- `POST /api/chat` - 发送消息并获取AI响应
- 请求体：`{"user_id": "用户ID", "message": "消息内容"}`

### 记忆管理接口
- `GET /api/memories?user_id=用户ID` - 获取所有记忆
- `GET /api/memories/search?user_id=用户ID&q=查询` - 搜索记忆
- `DELETE /api/memories?user_id=用户ID` - 清空所有记忆
- `DELETE /api/memories/{memory_id}` - 删除特定记忆

### WebSocket事件
- `connect` - 客户端连接
- `disconnect` - 客户端断开
- `join_user` - 用户加入
- `memories_updated` - 记忆更新通知
- `log_message` - 日志消息推送

## 配置说明

### Mem0配置
应用使用以下配置：
```python
config = {
    "vector_store": {
        "provider": "chroma",
        "config": {
            "collection_name": "web_demo_05",
            "path": "./db/web_demo_05_db"
        }
    },
    "llm": {
        "provider": "ollama",
        "config": {
            "model": "qwen3:8b",
            "temperature": 0.1,
            "ollama_base_url": "http://************:8088"
        }
    },
    "embedder": {
        "provider": "ollama",
        "config": {
            "model": "qwen3:8b",
            "ollama_base_url": "http://************:8088"
        }
    }
}
```

### 端口配置
默认运行在端口5000，可以在`app.py`中修改：
```python
demo.run(host='0.0.0.0', port=5000, debug=True)
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查Ollama服务是否运行
   - 确认端口5000未被占用
   - 检查防火墙设置

2. **记忆操作失败**
   - 确认mem0配置正确
   - 检查数据库路径权限
   - 查看控制台错误日志

3. **WebSocket连接问题**
   - 刷新页面重新连接
   - 检查浏览器控制台错误
   - 确认服务器正常运行

### 日志查看
- 服务器日志：查看控制台输出
- 前端日志：打开浏览器开发者工具
- 系统日志：在应用的"日志"标签页查看

## 开发说明

### 文件结构
```
web_demo_05/
├── app.py              # Flask服务器主文件
├── requirements.txt    # Python依赖
├── README.md          # 说明文档
└── static/            # 静态文件
    ├── index.html     # 主页面
    ├── style.css      # 样式文件
    └── script.js      # 前端逻辑
```

### 扩展功能
- 可以添加更多的记忆分类
- 支持文件上传和处理
- 集成更多的LLM模型
- 添加用户认证系统
- 实现记忆导出功能

## 许可证

本项目遵循与mem0相同的许可证。
