#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web Demo 05 - Mem0记忆系统完整Web测试页面
提供对话界面、记忆展示和日志监控功能
"""

import os
import sys
import json
import logging
import asyncio
from datetime import datetime
from typing import Dict, List, Any
import uuid

from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_socketio import SocketIO, emit
from flask_cors import CORS
from config import get_config

# 添加mem0路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../mem0'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

try:
    from mem0 import Memory
except ImportError as e:
    print(f"导入mem0失败: {e}")
    print("尝试使用本地路径导入...")
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))
        from mem0.mem0.memory.main import Memory
    except ImportError as e2:
        print(f"本地导入也失败: {e2}")
        print("将使用模拟的Memory类")

        class Memory:
            @classmethod
            def from_config(cls, config):
                return cls()

            def add(self, messages, user_id=None):
                return {"results": [{"memory": "模拟记忆", "id": "mock_id"}]}

            def get_all(self, user_id=None):
                return {"results": []}

            def search(self, query, user_id=None):
                return {"results": []}

            def delete(self, memory_id):
                return {"message": "模拟删除成功"}

            def delete_all(self, user_id=None):
                return {"message": "模拟清空成功"}

class WebSocketLogHandler(logging.Handler):
    """自定义日志处理器，将日志通过WebSocket发送到前端"""
    
    def __init__(self, socketio):
        super().__init__()
        self.socketio = socketio
        
    def emit(self, record):
        try:
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'level': record.levelname,
                'message': self.format(record),
                'module': record.module
            }
            self.socketio.emit('log_message', log_entry)
        except Exception:
            pass

class Mem0WebDemo:
    """Mem0 Web演示应用"""
    
    def __init__(self):
        self.config_class = get_config()
        self.app = Flask(__name__, static_folder='static', template_folder='static')
        self.app.config.from_object(self.config_class)

        # 启用CORS
        CORS(self.app)
        
        # 初始化SocketIO
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # 设置日志
        self.setup_logging()
        
        # 初始化Memory
        self.setup_memory()
        
        # 注册路由
        self.register_routes()
        
        # 注册WebSocket事件
        self.register_socketio_events()
        
        # 存储活跃的用户会话
        self.active_sessions = {}
        
        self.logger.info("Mem0 Web Demo 05 初始化完成")
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建logger
        self.logger = logging.getLogger('mem0_web_demo')
        self.logger.setLevel(logging.INFO)
        
        # 清除现有handlers
        self.logger.handlers.clear()
        
        # 控制台handler
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # WebSocket handler将在socketio初始化后添加
        
    def setup_memory(self):
        """初始化Memory配置"""
        try:
            self.mem0_config = self.config_class.MEM0_CONFIG
            self.memory = Memory.from_config(self.mem0_config)
            self.logger.info("Memory配置初始化成功")

        except Exception as e:
            self.logger.error(f"Memory初始化失败: {e}")
            self.memory = None
    
    def register_routes(self):
        """注册HTTP路由"""
        
        @self.app.route('/')
        def index():
            """主页面"""
            return send_from_directory('static', 'index.html')
        
        @self.app.route('/api/chat', methods=['POST'])
        def chat():
            """处理对话请求"""
            try:
                data = request.get_json()
                user_id = data.get('user_id', 'default_user')
                message = data.get('message', '')
                
                if not message.strip():
                    return jsonify({'error': '消息不能为空'}), 400
                
                self.logger.info(f"收到用户 {user_id} 的消息: {message}")
                
                # 模拟AI响应（这里可以集成真实的LLM）
                ai_response = f"我理解了您的消息：{message}。我会记住这个信息。"
                
                # 添加到记忆系统
                if self.memory:
                    messages = [
                        {"role": "user", "content": message},
                        {"role": "assistant", "content": ai_response}
                    ]
                    
                    try:
                        result = self.memory.add(messages, user_id=user_id)
                        self.logger.info(f"记忆添加结果: {result}")
                        
                        # 通过WebSocket通知前端更新记忆
                        self.socketio.emit('memories_updated', {
                            'user_id': user_id,
                            'action': 'add',
                            'result': result
                        })
                        
                    except Exception as e:
                        self.logger.error(f"添加记忆失败: {e}")
                
                return jsonify({
                    'response': ai_response,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                self.logger.error(f"处理对话请求失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/memories', methods=['GET'])
        def get_memories():
            """获取所有记忆"""
            try:
                user_id = request.args.get('user_id', 'default_user')
                
                if not self.memory:
                    return jsonify({'memories': [], 'error': 'Memory系统未初始化'})
                
                memories = self.memory.get_all(user_id=user_id)
                memories_list = memories.get("results", [])
                
                self.logger.info(f"获取用户 {user_id} 的记忆，共 {len(memories_list)} 条")
                
                return jsonify({
                    'memories': memories_list,
                    'count': len(memories_list)
                })
                
            except Exception as e:
                self.logger.error(f"获取记忆失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/memories/search', methods=['GET'])
        def search_memories():
            """搜索记忆"""
            try:
                user_id = request.args.get('user_id', 'default_user')
                query = request.args.get('q', '')
                
                if not query.strip():
                    return jsonify({'error': '搜索查询不能为空'}), 400
                
                if not self.memory:
                    return jsonify({'memories': [], 'error': 'Memory系统未初始化'})
                
                search_result = self.memory.search(query, user_id=user_id)
                memories_list = search_result.get("results", [])
                
                self.logger.info(f"搜索用户 {user_id} 的记忆，查询: {query}，结果: {len(memories_list)} 条")
                
                return jsonify({
                    'memories': memories_list,
                    'query': query,
                    'count': len(memories_list)
                })
                
            except Exception as e:
                self.logger.error(f"搜索记忆失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/memories/<memory_id>', methods=['DELETE'])
        def delete_memory(memory_id):
            """删除特定记忆"""
            try:
                if not self.memory:
                    return jsonify({'error': 'Memory系统未初始化'}), 500
                
                result = self.memory.delete(memory_id)
                self.logger.info(f"删除记忆 {memory_id}: {result}")
                
                # 通过WebSocket通知前端更新
                self.socketio.emit('memories_updated', {
                    'action': 'delete',
                    'memory_id': memory_id,
                    'result': result
                })
                
                return jsonify(result)
                
            except Exception as e:
                self.logger.error(f"删除记忆失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/memories', methods=['DELETE'])
        def clear_memories():
            """清空所有记忆"""
            try:
                user_id = request.args.get('user_id', 'default_user')
                
                if not self.memory:
                    return jsonify({'error': 'Memory系统未初始化'}), 500
                
                result = self.memory.delete_all(user_id=user_id)
                self.logger.info(f"清空用户 {user_id} 的所有记忆: {result}")
                
                # 通过WebSocket通知前端更新
                self.socketio.emit('memories_updated', {
                    'user_id': user_id,
                    'action': 'clear',
                    'result': result
                })
                
                return jsonify(result)
                
            except Exception as e:
                self.logger.error(f"清空记忆失败: {e}")
                return jsonify({'error': str(e)}), 500
    
    def register_socketio_events(self):
        """注册WebSocket事件"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """客户端连接"""
            session_id = request.sid
            self.active_sessions[session_id] = {
                'connected_at': datetime.now().isoformat(),
                'user_id': None
            }
            
            self.logger.info(f"客户端连接: {session_id}")
            emit('connected', {'session_id': session_id})
            
            # 添加WebSocket日志处理器
            if not hasattr(self, 'ws_log_handler'):
                self.ws_log_handler = WebSocketLogHandler(self.socketio)
                self.ws_log_handler.setFormatter(logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                ))
                self.logger.addHandler(self.ws_log_handler)
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """客户端断开连接"""
            session_id = request.sid
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            
            self.logger.info(f"客户端断开连接: {session_id}")
        
        @self.socketio.on('join_user')
        def handle_join_user(data):
            """用户加入"""
            session_id = request.sid
            user_id = data.get('user_id', 'default_user')
            
            if session_id in self.active_sessions:
                self.active_sessions[session_id]['user_id'] = user_id
            
            self.logger.info(f"用户 {user_id} 加入会话 {session_id}")
            emit('user_joined', {'user_id': user_id})
    
    def run(self):
        """运行应用"""
        host = self.config_class.HOST
        port = self.config_class.PORT
        debug = self.config_class.DEBUG
        self.logger.info(f"启动Mem0 Web Demo 05，地址: http://{host}:{port}")
        self.socketio.run(self.app, host=host, port=port, debug=debug)

if __name__ == '__main__':
    demo = Mem0WebDemo()
    demo.run()
