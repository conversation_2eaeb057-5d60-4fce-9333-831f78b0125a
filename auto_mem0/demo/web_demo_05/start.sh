#!/bin/bash

# Mem0 Web Demo 05 启动脚本

echo "=== Mem0 Web Demo 05 启动脚本 ==="
echo ""

# 检查Python版本
echo "检查Python版本..."
python3 --version
if [ $? -ne 0 ]; then
    echo "错误: 未找到Python3，请先安装Python 3.8+"
    exit 1
fi

# 检查是否在正确的目录
if [ ! -f "app.py" ]; then
    echo "错误: 请在web_demo_05目录下运行此脚本"
    exit 1
fi

# 创建数据库目录
echo "创建数据库目录..."
mkdir -p db

# 检查依赖是否安装
echo "检查依赖..."
python3 -c "import flask, flask_socketio, flask_cors" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖安装失败"
        exit 1
    fi
fi

# 检查Ollama服务
echo "检查Ollama服务..."
curl -s http://************:8088/api/tags > /dev/null
if [ $? -ne 0 ]; then
    echo "警告: 无法连接到Ollama服务 (http://************:8088)"
    echo "请确保Ollama服务正在运行"
    echo ""
fi

# 启动应用
echo "启动Mem0 Web Demo 05..."
echo "访问地址: http://localhost:5000"
echo "按 Ctrl+C 停止服务"
echo ""

python3 app.py
