# Mem0 Web Demo 05 环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# Flask配置
SECRET_KEY=your-secret-key-here
DEBUG=True
FLASK_ENV=development

# 服务器配置
HOST=0.0.0.0
PORT=5000

# Mem0配置
COLLECTION_NAME=web_demo_05
DB_PATH=./db/web_demo_05_db

# LLM配置
LLM_MODEL=qwen3:8b
LLM_TEMPERATURE=0.1
EMBEDDER_MODEL=qwen3:8b
OLLAMA_URL=http://************:8088

# 日志配置
LOG_LEVEL=INFO
LOG_MAX_ENTRIES=1000

# WebSocket配置
CORS_ORIGINS=*
