"""
短期记忆和长期记忆测试Demo
演示如何使用mem0实现不同类型的记忆管理
"""

import sys
import os
import time
import datetime
from typing import Dict, List, Any

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

import logging
from mem0 import Memory

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MemoryTypesDemo:
    """演示短期记忆和长期记忆的使用，支持记忆分类管理"""

    def __init__(self, allowed_categories=None):
        """
        初始化两个不同的Memory实例来模拟短期和长期记忆

        Args:
            allowed_categories (list): 允许的记忆类别列表，如果为None则允许所有类别
        """
        # 设置允许的记忆类别
        if allowed_categories is None:
            self.allowed_categories = [
                "personal_info",      # 个人信息
                "preferences",        # 偏好设置
                "work_tasks",         # 工作任务
                "learning",           # 学习内容
                "conversations",      # 对话记录
                "reminders",          # 提醒事项
                "technical_knowledge", # 技术知识
                "general"             # 通用类别
            ]
        else:
            self.allowed_categories = allowed_categories

        print(f"📋 初始化记忆系统，支持的类别: {', '.join(self.allowed_categories)}")
        
        # 短期记忆配置 - 使用内存存储，会话结束后消失
        self.short_term_config = {
            "vector_store": {
                "provider": "chroma",
                "config": {
                    "collection_name": "short_term_memories",
                    "path": "./db/:memory:"  # 使用内存存储
                }
            },
            "llm": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "temperature": 0.1,
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            },
            "embedder": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            }
        }
        
        # 长期记忆配置 - 使用持久化存储
        self.long_term_config = {
            "vector_store": {
                "provider": "chroma",
                "config": {
                    "collection_name": "long_term_memories",
                    "path": "./db/long_term_chroma_db"  # 持久化存储
                }
            },
            "llm": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "temperature": 0.1,
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            },
            "embedder": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            }
        }
        
        # 创建Memory实例
        self.short_term_memory = Memory.from_config(self.short_term_config)
        self.long_term_memory = Memory.from_config(self.long_term_config)
        
        self.user_id = "demo_user"

    def validate_category(self, category: str) -> bool:
        """验证类别是否在允许的类别列表中"""
        return category in self.allowed_categories

    def get_category_from_content(self, content: str) -> str:
        """
        基于内容智能推断记忆类别
        这里使用简单的关键词匹配，实际应用中可以使用LLM进行分类
        """
        content_lower = content.lower()

        # 个人信息关键词
        if any(keyword in content_lower for keyword in ["我叫", "我是", "我的名字", "年龄", "住在"]):
            return "personal_info"

        # 偏好设置关键词
        elif any(keyword in content_lower for keyword in ["喜欢", "不喜欢", "偏好", "爱好", "讨厌"]):
            return "preferences"

        # 工作任务关键词
        elif any(keyword in content_lower for keyword in ["工作", "任务", "项目", "deadline", "会议", "开发"]):
            return "work_tasks"

        # 学习内容关键词
        elif any(keyword in content_lower for keyword in ["学习", "课程", "教程", "知识", "技能"]):
            return "learning"

        # 提醒事项关键词
        elif any(keyword in content_lower for keyword in ["提醒", "记得", "别忘了", "明天", "下周"]):
            return "reminders"

        # 技术知识关键词
        elif any(keyword in content_lower for keyword in ["编程", "代码", "算法", "数据库", "API", "框架"]):
            return "technical_knowledge"

        # 默认为通用类别
        else:
            return "general"
        
    def add_short_term_memory(self, messages: List[Dict], category: str = None, metadata: Dict = None, expiration_hours: int = 1):
        """
        添加短期记忆 - 用于当前会话的临时信息，支持自动过期和分类

        Args:
            messages: 消息列表
            category: 记忆类别，如果为None则自动推断
            metadata: 额外的元数据
            expiration_hours: 过期时间（小时）
        """
        if metadata is None:
            metadata = {}

        # 自动推断类别（如果未指定）
        if category is None:
            # 从消息内容中推断类别
            content = " ".join([msg.get('content', '') for msg in messages])
            category = self.get_category_from_content(content)

        # 验证类别
        if not self.validate_category(category):
            print(f"❌ 错误：类别 '{category}' 不在允许的类别列表中")
            print(f"   允许的类别: {', '.join(self.allowed_categories)}")
            return None

        # 计算过期时间
        expiration_time = datetime.datetime.now() + datetime.timedelta(hours=expiration_hours)

        # 为短期记忆添加特殊标记
        metadata.update({
            "memory_type": "short_term",
            "category": category,
            "session_id": f"session_{int(time.time())}",
            "created_at": datetime.datetime.now().isoformat(),
            "expiration_hours": expiration_hours,
            "expires_at": expiration_time.isoformat()
        })

        print(f"\n📝 添加短期记忆 (类别: {category}, 过期时间: {expiration_hours}小时):")
        for msg in messages:
            print(f"  {msg['role']}: {msg['content']}")
        print(f"  📂 类别: {category}")
        print(f"  ⏰ 过期时间: {expiration_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # 添加记忆（本地版本不支持expiration_date参数）
        result = self.short_term_memory.add(
            messages,
            user_id=self.user_id,
            metadata=metadata
        )
        print(f"✅ 短期记忆添加结果: {len(result.get('results', []))} 条记忆")
        return result
    
    def add_long_term_memory(self, messages: List[Dict], category: str = None, metadata: Dict = None):
        """
        添加长期记忆 - 用于持久化的重要信息，支持分类

        Args:
            messages: 消息列表
            category: 记忆类别，如果为None则自动推断
            metadata: 额外的元数据
        """
        if metadata is None:
            metadata = {}

        # 自动推断类别（如果未指定）
        if category is None:
            # 从消息内容中推断类别
            content = " ".join([msg.get('content', '') for msg in messages])
            category = self.get_category_from_content(content)

        # 验证类别
        if not self.validate_category(category):
            print(f"❌ 错误：类别 '{category}' 不在允许的类别列表中")
            print(f"   允许的类别: {', '.join(self.allowed_categories)}")
            return None

        # 为长期记忆添加特殊标记
        metadata.update({
            "memory_type": "long_term",
            "category": category,
            "importance": "high",
            "created_at": datetime.datetime.now().isoformat()
        })

        print(f"\n💾 添加长期记忆 (类别: {category}):")
        for msg in messages:
            print(f"  {msg['role']}: {msg['content']}")
        print(f"  📂 类别: {category}")

        result = self.long_term_memory.add(messages, user_id=self.user_id, metadata=metadata)
        print(f"✅ 长期记忆添加结果: {len(result.get('results', []))} 条记忆")
        return result
    
    def search_short_term_memory(self, query: str):
        """搜索短期记忆"""
        print(f"\n🔍 搜索短期记忆: '{query}'")
        results = self.short_term_memory.search(query, user_id=self.user_id)
        
        print("📋 短期记忆搜索结果:")
        for i, result in enumerate(results.get("results", []), 1):
            print(f"  {i}. {result['memory']} (相关性: {result['score']:.2f})")
        
        return results
    
    def search_long_term_memory(self, query: str):
        """搜索长期记忆"""
        print(f"\n🔍 搜索长期记忆: '{query}'")
        results = self.long_term_memory.search(query, user_id=self.user_id)
        
        print("📚 长期记忆搜索结果:")
        for i, result in enumerate(results.get("results", []), 1):
            print(f"  {i}. {result['memory']} (相关性: {result['score']:.2f})")
        
        return results
    
    def get_all_short_term_memories(self):
        """获取所有短期记忆"""
        print(f"\n📋 所有短期记忆:")
        memories = self.short_term_memory.get_all(user_id=self.user_id)
        
        for i, mem in enumerate(memories.get("results", []), 1):
            print(f"  {i}. {mem['memory']}")
        
        return memories
    
    def get_all_long_term_memories(self):
        """获取所有长期记忆"""
        print(f"\n📚 所有长期记忆:")
        memories = self.long_term_memory.get_all(user_id=self.user_id)
        
        for i, mem in enumerate(memories.get("results", []), 1):
            print(f"  {i}. {mem['memory']}")
        
        return memories
    
    def clear_short_term_memory(self):
        """清除短期记忆"""
        print(f"\n🗑️ 清除短期记忆...")
        try:
            result = self.short_term_memory.delete_all(user_id=self.user_id)
            print(f"✅ 短期记忆已清除: {result}")
        except Exception as e:
            print(f"❌ 清除短期记忆时出错: {e}")
    
    def simulate_memory_expiration(self):
        """模拟记忆过期机制"""
        print(f"\n⏰ 模拟记忆过期...")
        
        # 添加一个带有过期时间的临时记忆
        temp_messages = [
            {"role": "user", "content": "我今天下午3点有个会议，记得提醒我"},
            {"role": "assistant", "content": "好的，我会记住你下午3点的会议"}
        ]
        
        # 使用metadata来标记过期时间
        expiration_time = datetime.datetime.now() + datetime.timedelta(seconds=10)  # 10秒后过期
        metadata = {
            "expiration_time": expiration_time.isoformat(),
            "is_temporary": True
        }
        
        self.add_short_term_memory(temp_messages, metadata)
        
        print(f"⏳ 等待10秒模拟记忆过期...")
        time.sleep(11)
        
        print(f"🔍 检查过期记忆是否还存在...")
        results = self.search_short_term_memory("会议")
        
        if not results.get("results"):
            print("✅ 临时记忆已过期（在实际应用中需要实现过期清理机制）")
        else:
            print("ℹ️ 记忆仍然存在（需要实现自动过期清理机制）")

def main():
    """主函数 - 演示短期记忆和长期记忆的使用"""
    print("=" * 80)
    print("🧠 短期记忆和长期记忆测试Demo")
    print("=" * 80)
    
    demo = MemoryTypesDemo()
    
    # 1. 添加长期记忆 - 用户基本信息
    print("\n" + "=" * 50)
    print("1️⃣ 添加长期记忆 - 用户基本信息")
    print("=" * 50)
    
    long_term_messages = [
        {"role": "user", "content": "我叫张三，是一名软件工程师，住在北京，喜欢喝咖啡和阅读技术书籍"},
        {"role": "assistant", "content": "记住了你的基本信息"}
    ]
    
    demo.add_long_term_memory(long_term_messages, {"category": "personal_info"})
    
    # 2. 添加短期记忆 - 当前会话信息
    print("\n" + "=" * 50)
    print("2️⃣ 添加短期记忆 - 当前会话信息")
    print("=" * 50)
    
    short_term_messages = [
        {"role": "user", "content": "我现在正在调试一个Python程序，遇到了内存泄漏问题"},
        {"role": "assistant", "content": "了解，你正在处理Python内存泄漏问题"}
    ]
    
    demo.add_short_term_memory(short_term_messages, {"category": "current_task"})
    
    # 3. 搜索测试
    print("\n" + "=" * 50)
    print("3️⃣ 搜索测试")
    print("=" * 50)
    
    demo.search_long_term_memory("用户的职业是什么？")
    demo.search_short_term_memory("用户现在在做什么？")
    
    # 4. 查看所有记忆
    print("\n" + "=" * 50)
    print("4️⃣ 查看所有记忆")
    print("=" * 50)
    
    demo.get_all_long_term_memories()
    demo.get_all_short_term_memories()
    
    # 5. 添加更多短期记忆
    print("\n" + "=" * 50)
    print("5️⃣ 添加更多短期记忆")
    print("=" * 50)
    
    more_short_term = [
        {"role": "user", "content": "我找到问题了，是因为没有正确关闭文件句柄"},
        {"role": "assistant", "content": "很好，文件句柄泄漏确实会导致内存问题"}
    ]
    
    demo.add_short_term_memory(more_short_term, {"category": "problem_solving"})
    
    # 6. 模拟记忆过期
    print("\n" + "=" * 50)
    print("6️⃣ 模拟记忆过期机制")
    print("=" * 50)
    
    demo.simulate_memory_expiration()
    
    # 7. 最终状态检查
    print("\n" + "=" * 50)
    print("7️⃣ 最终状态检查")
    print("=" * 50)
    
    print("\n🔍 搜索长期记忆中的用户信息:")
    demo.search_long_term_memory("张三")
    
    print("\n🔍 搜索短期记忆中的技术问题:")
    demo.search_short_term_memory("Python 内存")
    
    # 8. 清理短期记忆（可选）
    print("\n" + "=" * 50)
    print("8️⃣ 清理短期记忆")
    print("=" * 50)
    
    user_input = input("是否清除短期记忆？(y/n): ")
    if user_input.lower() == 'y':
        demo.clear_short_term_memory()
        print("✅ 短期记忆已清除，长期记忆保持不变")
    
    print("\n" + "=" * 80)
    print("🎉 Demo完成！")
    print("📝 总结:")
    print("  - 长期记忆：存储用户基本信息，持久化保存")
    print("  - 短期记忆：存储当前会话信息，可以清除")
    print("  - 不同的存储配置实现了记忆类型的区分")
    print("=" * 80)

if __name__ == "__main__":
    main()
