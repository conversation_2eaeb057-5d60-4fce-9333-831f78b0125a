"""
Event处理Demo
展示如何正确处理记忆的ADD、UPDATE、DELETE、NONE等事件类型
"""

import sys
import os
import time
import datetime
from typing import Dict, List, Any, Optional
from enum import Enum

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

import logging
from enhanced_memory import EnhancedMemory, create_classifier_function

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MemoryCategory(Enum):
    """记忆类别枚举"""
    PERSONAL_INFO = "personal_info"
    PREFERENCES = "preferences"
    WORK_TASKS = "work_tasks"
    TECHNICAL_KNOWLEDGE = "technical_knowledge"
    LEARNING = "learning"
    GENERAL = "general"

class EventHandlingDemo:
    """Event处理演示类"""
    
    def __init__(self):
        """初始化演示类"""
        
        print(f"📋 Event处理演示初始化")
        
        # 初始化增强的Memory
        config = {
            "vector_store": {
                "provider": "chroma",
                "config": {
                    "collection_name": "event_handling",
                    "path": "./db/event_handling_db"
                }
            },
            "llm": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "temperature": 0.1,
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            },
            "embedder": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            }
        }
        
        self.enhanced_memory = EnhancedMemory.from_config(config)
        self.user_id = "event_demo_user"
        
        # 定义分类关键词
        self.category_keywords = {
            MemoryCategory.PERSONAL_INFO.value: ["姓名", "年龄", "职业", "住址"],
            MemoryCategory.PREFERENCES.value: ["喜欢", "不喜欢", "偏好", "爱好"],
            MemoryCategory.WORK_TASKS.value: ["工作", "任务", "项目", "deadline"],
            MemoryCategory.TECHNICAL_KNOWLEDGE.value: ["技术", "编程", "代码", "框架"],
            MemoryCategory.LEARNING.value: ["学习", "课程", "技能", "知识"],
            MemoryCategory.GENERAL.value: []
        }
        
        # 创建分类函数
        allowed_categories = [cat.value for cat in MemoryCategory]
        self.classifier = create_classifier_function(
            self.category_keywords, 
            allowed_categories, 
            MemoryCategory.GENERAL.value
        )
    
    def demo_add_events(self):
        """演示ADD事件"""
        print(f"\n" + "=" * 60)
        print("1️⃣ 演示ADD事件 - 添加新记忆")
        print("=" * 60)
        
        conversation = [
            {"role": "user", "content": "我叫李四，是一名数据科学家"},
            {"role": "assistant", "content": "很高兴认识你李四"}
        ]
        
        print(f"\n📝 输入对话:")
        for i, msg in enumerate(conversation, 1):
            print(f"  {i}. {msg['role']}: {msg['content']}")
        
        # 提取记忆
        extracted_memories = self.enhanced_memory.extract_memories(conversation, user_id=self.user_id)
        
        print(f"\n📋 提取到的记忆及其事件:")
        for i, memory in enumerate(extracted_memories, 1):
            print(f"  {i}. [{memory.event}] {memory.text}")
        
        # 分类和存储
        for memory in extracted_memories:
            category, metadata = self.classifier(memory.text)
            memory.set_category(category).set_metadata(metadata)
        
        result = self.enhanced_memory.store_memories(extracted_memories, user_id=self.user_id)
        
        return result
    
    def demo_update_events(self):
        """演示UPDATE事件"""
        print(f"\n" + "=" * 60)
        print("2️⃣ 演示UPDATE事件 - 更新现有记忆")
        print("=" * 60)
        
        # 添加更新信息的对话
        conversation = [
            {"role": "user", "content": "我现在是高级数据科学家了，刚刚升职"},
            {"role": "assistant", "content": "恭喜你升职！"}
        ]
        
        print(f"\n📝 输入对话:")
        for i, msg in enumerate(conversation, 1):
            print(f"  {i}. {msg['role']}: {msg['content']}")
        
        # 提取记忆
        extracted_memories = self.enhanced_memory.extract_memories(conversation, user_id=self.user_id)
        
        print(f"\n📋 提取到的记忆及其事件:")
        for i, memory in enumerate(extracted_memories, 1):
            print(f"  {i}. [{memory.event}] {memory.text}")
            if memory.old_memory:
                print(f"      旧记忆: {memory.old_memory}")
        
        # 分类和存储
        for memory in extracted_memories:
            category, metadata = self.classifier(memory.text)
            memory.set_category(category).set_metadata(metadata)
        
        result = self.enhanced_memory.store_memories(extracted_memories, user_id=self.user_id)
        
        return result
    
    def demo_mixed_events(self):
        """演示混合事件"""
        print(f"\n" + "=" * 60)
        print("3️⃣ 演示混合事件 - ADD、UPDATE、NONE混合")
        print("=" * 60)
        
        conversation = [
            {"role": "user", "content": "我现在在学习机器学习，特别是深度学习。我还是喜欢Python编程"},
            {"role": "assistant", "content": "很好，持续学习很重要"}
        ]
        
        print(f"\n📝 输入对话:")
        for i, msg in enumerate(conversation, 1):
            print(f"  {i}. {msg['role']}: {msg['content']}")
        
        # 提取记忆
        extracted_memories = self.enhanced_memory.extract_memories(conversation, user_id=self.user_id)
        
        print(f"\n📋 提取到的记忆及其事件:")
        for i, memory in enumerate(extracted_memories, 1):
            print(f"  {i}. [{memory.event}] {memory.text}")
            if memory.old_memory:
                print(f"      旧记忆: {memory.old_memory}")
        
        # 分类和存储
        for memory in extracted_memories:
            category, metadata = self.classifier(memory.text)
            memory.set_category(category).set_metadata(metadata)
        
        result = self.enhanced_memory.store_memories(extracted_memories, user_id=self.user_id)
        
        return result
    
    def show_event_statistics(self):
        """显示事件统计"""
        print(f"\n" + "=" * 60)
        print("📊 事件统计")
        print("=" * 60)
        
        all_memories = self.enhanced_memory.get_all(user_id=self.user_id)
        
        event_stats = {}
        category_stats = {}
        
        for memory in all_memories.get("results", []):
            metadata = memory.get('metadata', {})
            
            # 统计事件类型（从历史记录中获取）
            # 这里简化处理，实际应该从history表中获取
            event = "ADD"  # 默认为ADD
            category = metadata.get('category', 'unknown')
            
            if event not in event_stats:
                event_stats[event] = 0
            event_stats[event] += 1
            
            if category not in category_stats:
                category_stats[category] = {"ADD": 0, "UPDATE": 0, "DELETE": 0}
            category_stats[category][event] += 1
        
        print(f"\n📈 事件类型统计:")
        for event, count in event_stats.items():
            print(f"  {event}: {count} 条")
        
        print(f"\n📂 分类事件统计:")
        for category, stats in category_stats.items():
            total = sum(stats.values())
            print(f"  {category}: {total} 条 (ADD:{stats['ADD']}, UPDATE:{stats['UPDATE']}, DELETE:{stats['DELETE']})")
    
    def demo_event_visualization(self, results_list):
        """演示事件可视化"""
        print(f"\n" + "=" * 60)
        print("🎨 事件可视化")
        print("=" * 60)
        
        all_events = []
        for result in results_list:
            if result and 'results' in result:
                all_events.extend(result['results'])
        
        print(f"\n📋 所有记忆事件时间线:")
        for i, event in enumerate(all_events, 1):
            event_type = event.get('event', 'UNKNOWN')
            memory_text = event.get('memory', '')
            category = event.get('category', 'unknown')
            
            # 事件图标
            icon = {
                'ADD': '➕',
                'UPDATE': '🔄',
                'DELETE': '❌',
                'NONE': '⚪',
                'GET': '👁️'
            }.get(event_type, '❓')
            
            print(f"  {i}. {icon} [{event_type}] [{category}] {memory_text}")
            
            if event.get('old_memory'):
                print(f"      📝 更新前: {event['old_memory']}")

def main():
    """主演示函数"""
    print("=" * 80)
    print("🧠 Event处理记忆管理Demo")
    print("=" * 80)
    
    # 初始化演示
    demo = EventHandlingDemo()
    
    # 清理之前的数据
    print("\n🗑️ 清理之前的测试数据...")
    try:
        demo.enhanced_memory.delete_all(user_id=demo.user_id)
        print("✅ 清理完成")
    except:
        print("✅ 无需清理")
    
    # 收集所有结果用于最终展示
    results = []
    
    # 1. 演示ADD事件
    result1 = demo.demo_add_events()
    results.append(result1)
    
    # 2. 演示UPDATE事件
    result2 = demo.demo_update_events()
    results.append(result2)
    
    # 3. 演示混合事件
    result3 = demo.demo_mixed_events()
    results.append(result3)
    
    # 4. 显示统计
    demo.show_event_statistics()
    
    # 5. 事件可视化
    demo.demo_event_visualization(results)
    
    print(f"\n" + "=" * 80)
    print("🎉 Event处理Demo完成！")
    print("📝 演示的功能:")
    print("  ✅ ADD事件 - 添加新记忆")
    print("  ✅ UPDATE事件 - 更新现有记忆")
    print("  ✅ 混合事件 - 一个对话包含多种事件类型")
    print("  ✅ 事件统计 - 统计各种事件类型")
    print("  ✅ 事件可视化 - 显示事件时间线")
    print("  ✅ 正确处理old_memory信息")
    print("=" * 80)

if __name__ == "__main__":
    main()
