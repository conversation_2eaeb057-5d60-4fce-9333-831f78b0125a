"""
测试程序04：仅验证配置文件
只验证JSON配置文件的正确性，不依赖LLM
"""

import json
import os

def test_config_file():
    """测试配置文件的正确性"""
    
    print("=" * 60)
    print("🧪 测试程序04：配置文件验证")
    print("=" * 60)
    
    # 1. 检查文件是否存在
    config_file = "memory_categories.json"
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    print(f"✅ 配置文件存在: {config_file}")
    
    # 2. 加载JSON配置
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            categories_config = json.load(f)
        print(f"✅ JSON格式正确，成功加载 {len(categories_config)} 个分类")
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 加载配置失败: {e}")
        return False
    
    # 3. 验证期望的分类
    expected_categories = [
        "food",                              # 美食偏好
        "beverage",                          # 饮品偏好
        "natural_landscape_preference",      # 自然景观偏好
        "human_landscape_preference",        # 人文景观偏好
        "entertainment_landscape_preference", # 娱乐景观偏好
        "travel_activity",                   # 游玩偏好
        "sport_team",                        # 球队偏好
        "athlete",                           # 球员偏好
        "sport_type",                        # 体育类型偏好
        "news_topic",                        # 新闻话题偏好
        "news_type",                         # 新闻类型偏好
        "entertainment_venues_preference"    # 娱乐场所偏好
    ]
    
    print(f"\n🔍 验证期望的 {len(expected_categories)} 个分类:")
    
    missing_categories = []
    extra_categories = []
    
    # 检查缺失的分类
    for category in expected_categories:
        if category in categories_config:
            print(f"  ✅ {category}")
        else:
            print(f"  ❌ {category} (缺失)")
            missing_categories.append(category)
    
    # 检查额外的分类
    for category in categories_config:
        if category not in expected_categories:
            print(f"  ⚠️ {category} (额外)")
            extra_categories.append(category)
    
    # 4. 验证每个分类的结构
    print(f"\n📋 验证分类结构:")
    
    structure_errors = []
    for category, config in categories_config.items():
        print(f"\n  📂 {category}:")
        
        # 检查必要字段
        if not isinstance(config, dict):
            print(f"    ❌ 配置不是字典格式")
            structure_errors.append(f"{category}: 配置格式错误")
            continue
        
        # 检查description字段
        if "description" not in config:
            print(f"    ❌ 缺少description字段")
            structure_errors.append(f"{category}: 缺少description")
        elif not config["description"]:
            print(f"    ❌ description为空")
            structure_errors.append(f"{category}: description为空")
        else:
            desc_len = len(config["description"])
            print(f"    ✅ description: {desc_len}字符")
        
        # 检查examples字段
        if "examples" not in config:
            print(f"    ❌ 缺少examples字段")
            structure_errors.append(f"{category}: 缺少examples")
        elif not config["examples"]:
            print(f"    ❌ examples为空")
            structure_errors.append(f"{category}: examples为空")
        else:
            examples_len = len(config["examples"])
            print(f"    ✅ examples: {examples_len}字符")
    
    # 5. 生成测试报告
    print(f"\n📊 测试报告:")
    print(f"  总分类数: {len(categories_config)}")
    print(f"  期望分类数: {len(expected_categories)}")
    print(f"  缺失分类: {len(missing_categories)}")
    print(f"  额外分类: {len(extra_categories)}")
    print(f"  结构错误: {len(structure_errors)}")
    
    if missing_categories:
        print(f"\n❌ 缺失的分类: {missing_categories}")
    
    if extra_categories:
        print(f"\n⚠️ 额外的分类: {extra_categories}")
    
    if structure_errors:
        print(f"\n❌ 结构错误:")
        for error in structure_errors:
            print(f"    - {error}")
    
    # 6. 显示分类映射
    print(f"\n📝 分类映射表:")
    category_mapping = {
        "food": "美食偏好",
        "beverage": "饮品偏好",
        "natural_landscape_preference": "自然景观偏好",
        "human_landscape_preference": "人文景观偏好", 
        "entertainment_landscape_preference": "娱乐景观偏好",
        "travel_activity": "游玩偏好",
        "sport_team": "球队偏好",
        "athlete": "球员偏好",
        "sport_type": "体育类型偏好",
        "news_topic": "新闻话题偏好",
        "news_type": "新闻类型偏好",
        "entertainment_venues_preference": "娱乐场所偏好"
    }
    
    for category in categories_config:
        chinese_name = category_mapping.get(category, "未知分类")
        print(f"  {category} → {chinese_name}")
    
    # 7. 判断测试结果
    success = (
        len(missing_categories) == 0 and 
        len(structure_errors) == 0 and
        len(categories_config) == len(expected_categories)
    )
    
    if success:
        print(f"\n🎉 配置文件验证通过！")
        print(f"✅ 所有 {len(expected_categories)} 个分类都正确配置")
        return True
    else:
        print(f"\n⚠️ 配置文件存在问题")
        if missing_categories:
            print(f"❌ 需要添加缺失的分类")
        if structure_errors:
            print(f"❌ 需要修复结构错误")
        return False

if __name__ == "__main__":
    success = test_config_file()
    if success:
        print("\n" + "=" * 60)
        print("🎯 测试程序04执行成功")
        print("✅ 新分类配置文件验证通过")
        print("📝 包含12个偏好分类，结构完整")
        print("🚀 可以用于LLM智能分类系统")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 测试程序04执行失败")
        print("🔧 请修复配置文件中的问题")
        print("=" * 60)
