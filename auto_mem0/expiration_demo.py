"""
mem0自带过期功能演示
展示如何使用mem0的内置过期时间和自动清理机制
"""

import sys
import os
import time
import datetime
from typing import Dict, List, Any

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

import logging
from mem0 import Memory

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ExpirationDemo:
    """演示mem0的过期功能"""
    
    def __init__(self):
        """初始化Memory实例"""
        
        self.config = {
            "vector_store": {
                "provider": "chroma",
                "config": {
                    "collection_name": "expiration_demo",
                    "path": "./db/expiration_demo_db"
                }
            },
            "llm": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "temperature": 0.1,
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            },
            "embedder": {
                "provider": "ollama",
                "config": {
                    "model": "qwen3:8b",
                    "ollama_base_url": "http://10.151.5.243:8088"
                }
            }
        }
        
        self.memory = Memory.from_config(self.config)
        self.user_id = "expiration_user"
    
    def add_memory_with_expiration(self, content: str, expiration_days: int, category: str = "general"):
        """添加带有过期时间的记忆"""
        
        # 计算过期日期
        expiration_date = (datetime.datetime.now() + datetime.timedelta(days=expiration_days)).strftime("%Y-%m-%d")
        
        messages = [
            {"role": "user", "content": content},
            {"role": "assistant", "content": f"记住了这个信息，将在{expiration_days}天后过期"}
        ]
        
        metadata = {
            "category": category,
            "expiration_days": expiration_days,
            "created_at": datetime.datetime.now().isoformat()
        }
        
        print(f"\n📝 添加记忆 (过期时间: {expiration_days}天)")
        print(f"   内容: {content}")
        print(f"   过期日期: {expiration_date}")
        
        # 将过期信息存储在metadata中（本地版本不支持expiration_date参数）
        metadata["expiration_date"] = expiration_date

        result = self.memory.add(
            messages,
            user_id=self.user_id,
            metadata=metadata
        )
        
        print(f"✅ 记忆添加成功: {len(result.get('results', []))} 条")
        return result
    
    def add_permanent_memory(self, content: str, category: str = "permanent"):
        """添加永久记忆（无过期时间）"""
        
        messages = [
            {"role": "user", "content": content},
            {"role": "assistant", "content": "记住了这个重要信息，永久保存"}
        ]
        
        metadata = {
            "category": category,
            "is_permanent": True,
            "created_at": datetime.datetime.now().isoformat()
        }
        
        print(f"\n💾 添加永久记忆:")
        print(f"   内容: {content}")
        
        result = self.memory.add(
            messages,
            user_id=self.user_id,
            metadata=metadata
            # 注意：不设置expiration_date，记忆将永久保存
        )
        
        print(f"✅ 永久记忆添加成功: {len(result.get('results', []))} 条")
        return result
    
    def search_memories(self, query: str):
        """搜索记忆（过期的记忆不会返回）"""
        print(f"\n🔍 搜索记忆: '{query}'")
        
        results = self.memory.search(query, user_id=self.user_id)
        
        print(f"📋 搜索结果 ({len(results.get('results', []))} 条):")
        for i, result in enumerate(results.get("results", []), 1):
            print(f"  {i}. {result['memory']} (相关性: {result['score']:.2f})")
        
        return results
    
    def get_all_memories(self):
        """获取所有有效记忆（不包括过期的）"""
        print(f"\n📚 所有有效记忆:")
        
        memories = self.memory.get_all(user_id=self.user_id)
        
        for i, mem in enumerate(memories.get("results", []), 1):
            print(f"  {i}. {mem['memory']}")
        
        print(f"总计: {len(memories.get('results', []))} 条有效记忆")
        return memories
    
    def cleanup_expired_memories_manual(self):
        """手动清理过期记忆的示例（实际上mem0会自动处理）"""
        print(f"\n🗑️ 手动清理过期记忆...")
        print("注意：mem0会自动处理过期记忆，这里只是演示概念")
        
        # 在实际应用中，mem0会自动过滤过期记忆
        # 这里只是演示如何实现自定义清理逻辑
        
        all_memories = self.memory.get_all(user_id=self.user_id)
        current_date = datetime.datetime.now().date()
        
        expired_count = 0
        for memory in all_memories.get("results", []):
            # 检查metadata中的过期信息
            metadata = memory.get('metadata', {})
            if 'expiration_days' in metadata:
                created_at = datetime.datetime.fromisoformat(metadata['created_at']).date()
                expiration_days = metadata['expiration_days']
                expiration_date = created_at + datetime.timedelta(days=expiration_days)
                
                if current_date > expiration_date:
                    expired_count += 1
                    print(f"  发现过期记忆: {memory['memory']}")
                    # 在实际应用中可以调用 self.memory.delete(memory['id'])
        
        if expired_count == 0:
            print("  没有发现过期记忆")
        else:
            print(f"  发现 {expired_count} 条过期记忆")
        
        print("✅ mem0会自动在搜索时过滤过期记忆")

def main():
    """主演示函数"""
    print("=" * 80)
    print("🧠 mem0自带过期功能演示")
    print("=" * 80)
    
    demo = ExpirationDemo()
    
    # 1. 添加不同过期时间的记忆
    print("\n" + "=" * 50)
    print("1️⃣ 添加不同过期时间的记忆")
    print("=" * 50)
    
    # 添加短期记忆（1天后过期）
    demo.add_memory_with_expiration(
        "我今天下午3点有个重要会议",
        expiration_days=1,
        category="schedule"
    )
    
    # 添加中期记忆（7天后过期）
    demo.add_memory_with_expiration(
        "我下周要出差去上海，需要准备相关文件",
        expiration_days=7,
        category="travel"
    )
    
    # 添加临时记忆（今天过期，用于测试）
    demo.add_memory_with_expiration(
        "临时测试信息，今天就过期",
        expiration_days=0,  # 今天过期
        category="test"
    )
    
    # 添加永久记忆
    demo.add_permanent_memory(
        "我的名字是张三，我是一名软件工程师",
        category="personal"
    )
    
    # 2. 立即搜索记忆
    print("\n" + "=" * 50)
    print("2️⃣ 立即搜索记忆")
    print("=" * 50)
    
    demo.search_memories("会议")
    demo.search_memories("出差")
    demo.search_memories("张三")
    
    # 3. 查看所有记忆
    print("\n" + "=" * 50)
    print("3️⃣ 查看所有有效记忆")
    print("=" * 50)
    
    demo.get_all_memories()
    
    # 4. 等待一段时间后再次搜索（模拟时间流逝）
    print("\n" + "=" * 50)
    print("4️⃣ 模拟时间流逝后的搜索")
    print("=" * 50)
    
    print("⏳ 等待几秒钟模拟时间流逝...")
    time.sleep(3)
    
    print("\n再次搜索记忆（过期的记忆应该不会返回）:")
    demo.search_memories("测试")
    demo.search_memories("会议")
    
    # 5. 演示手动清理概念
    print("\n" + "=" * 50)
    print("5️⃣ 过期记忆处理机制")
    print("=" * 50)
    
    demo.cleanup_expired_memories_manual()
    
    # 6. 最终状态
    print("\n" + "=" * 50)
    print("6️⃣ 最终记忆状态")
    print("=" * 50)
    
    demo.get_all_memories()
    
    print("\n" + "=" * 80)
    print("🎉 mem0过期功能演示完成！")
    print("📝 关键特性:")
    print("  ✅ 内置过期时间支持 (expiration_date)")
    print("  ✅ 自动过滤过期记忆")
    print("  ✅ 搜索时不返回过期内容")
    print("  ✅ 无需手动清理过期记忆")
    print("  ✅ 支持永久记忆和临时记忆")
    print("=" * 80)

if __name__ == "__main__":
    main()
