"""
测试程序02：新分类配置验证
验证更新后的分类配置（美食、饮品、景点、体育等偏好分类）
"""

import sys
import os
import json
import time

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem0')
sys.path.insert(0, mem0_path)

from enhanced_memory import create_classifier_function

def test_new_categories():
    """测试新的分类配置"""
    
    print("=" * 60)
    print("🧪 测试程序02：新分类配置验证")
    print("=" * 60)
    
    # 1. 加载新的分类配置
    print("\n📁 步骤1：加载新的分类配置")
    try:
        with open("memory_categories.json", 'r', encoding='utf-8') as f:
            categories_config = json.load(f)
        print(f"✅ 成功加载 {len(categories_config)} 个新分类类别")
        
        # 显示所有类别
        for i, (category, config) in enumerate(categories_config.items(), 1):
            description = config.get("description", "")
            print(f"  {i}. {category}: {description}")
        
    except Exception as e:
        print(f"❌ 加载配置文件失败: {e}")
        return False
    
    # 2. 创建LLM分类器
    print("\n🤖 步骤2：初始化LLM分类器")
    try:
        llm_config = {
            "model": "qwen3:8b",
            "temperature": 0.1,
            "ollama_base_url": "http://************:8088"
        }
        
        classifier = create_classifier_function(
            categories_config,
            default_category="food",  # 使用food作为默认类别
            llm_config=llm_config
        )
        print("✅ LLM分类器初始化成功")
        
    except Exception as e:
        print(f"❌ LLM分类器初始化失败: {e}")
        return False
    
    # 3. 测试各个新分类的样例
    print("\n🔬 步骤3：测试新分类功能")
    
    test_cases = [
        # 美食偏好
        ("我特别喜欢吃川菜，尤其是麻婆豆腐和水煮鱼", "food"),
        ("我不太能吃辣，比较喜欢清淡的粤菜", "food"),
        
        # 饮品偏好  
        ("我每天早上都要喝一杯拿铁咖啡", "beverage"),
        ("我比较喜欢喝绿茶，不太喝碳酸饮料", "beverage"),
        
        # 自然景观偏好
        ("我最喜欢去海边看日出日落，海景让我很放松", "natural_landscape_preference"),
        ("我特别向往大草原的辽阔，想去内蒙古看看", "natural_landscape_preference"),
        
        # 人文景观偏好
        ("我很喜欢参观古建筑，故宫和天坛都去过好几次", "human_landscape_preference"),
        ("我爱逛博物馆，特别是历史类的展览", "human_landscape_preference"),
        
        # 娱乐景观偏好
        ("我最想去迪士尼乐园，那些主题项目看起来很有趣", "entertainment_landscape_preference"),
        ("我喜欢去大型购物中心，既能购物又能娱乐", "entertainment_landscape_preference"),
        
        # 游玩偏好
        ("我比较喜欢自由行，不喜欢跟团的束缚感", "travel_activity"),
        ("我喜欢深度游，在一个地方慢慢体验当地文化", "travel_activity"),
        
        # 球队偏好
        ("我是巴塞罗那的铁杆球迷，梅西是我的偶像", "sport_team"),
        ("我支持湖人队，科比永远是我心中的传奇", "sport_team"),
        
        # 球员偏好
        ("我最喜欢的球员是C罗，他的职业精神很值得学习", "athlete"),
        ("库里的三分球技术真的太厉害了", "athlete"),
        
        # 体育类型偏好
        ("我平时最喜欢踢足球，周末经常和朋友踢球", "sport_type"),
        ("我比较喜欢网球，觉得这项运动很优雅", "sport_type"),
        
        # 新闻话题偏好
        ("我比较关注科技新闻，特别是AI和互联网相关的", "news_topic"),
        ("我喜欢看财经新闻，了解股市和经济动态", "news_topic"),
        
        # 新闻类型偏好
        ("我喜欢看视频新闻，比文字更直观", "news_type"),
        ("我更喜欢深度报道，不喜欢碎片化的信息", "news_type"),
        
        # 娱乐场所偏好
        ("我周末经常去电影院看电影，IMAX效果特别好", "entertainment_venues_preference"),
        ("我喜欢和朋友去KTV唱歌，很放松", "entertainment_venues_preference"),
    ]
    
    results = []
    total_time = 0
    correct_predictions = 0
    
    for i, (test_text, expected_category) in enumerate(test_cases, 1):
        print(f"\n  测试 {i}: '{test_text}'")
        print(f"    期望类别: {expected_category}")
        
        try:
            start_time = time.time()
            category, metadata = classifier(test_text)
            end_time = time.time()
            
            classification_time = end_time - start_time
            total_time += classification_time
            
            confidence = metadata.get("classification_confidence", 0)
            reasoning = metadata.get("classification_reasoning", "")
            method = metadata.get("classification_method", "")
            
            is_correct = category == expected_category
            if is_correct:
                correct_predictions += 1
                status = "✅"
            else:
                status = "❌"
            
            print(f"    {status} 预测类别: {category}")
            print(f"    🎯 置信度: {confidence:.2f}")
            print(f"    🔧 方法: {method}")
            print(f"    ⏱️ 耗时: {classification_time:.2f}秒")
            print(f"    💭 理由: {reasoning[:80]}...")
            
            results.append({
                "text": test_text,
                "expected": expected_category,
                "predicted": category,
                "confidence": confidence,
                "method": method,
                "time": classification_time,
                "correct": is_correct,
                "success": True
            })
            
        except Exception as e:
            print(f"    ❌ 分类失败: {e}")
            results.append({
                "text": test_text,
                "expected": expected_category,
                "predicted": "error",
                "confidence": 0,
                "method": "error",
                "time": 0,
                "correct": False,
                "success": False
            })
    
    # 4. 统计结果
    print("\n📊 步骤4：统计测试结果")
    
    successful_results = [r for r in results if r["success"]]
    failed_results = [r for r in results if not r["success"]]
    
    accuracy = correct_predictions / len(test_cases) * 100 if test_cases else 0
    
    print(f"  总测试数: {len(test_cases)}")
    print(f"  成功分类: {len(successful_results)}")
    print(f"  失败数量: {len(failed_results)}")
    print(f"  正确预测: {correct_predictions}")
    print(f"  分类准确率: {accuracy:.1f}%")
    print(f"  总耗时: {total_time:.2f}秒")
    print(f"  平均耗时: {total_time/len(successful_results):.2f}秒/条" if successful_results else "N/A")
    
    # 5. 分类分布统计
    if successful_results:
        print(f"\n📈 预测分类分布:")
        category_counts = {}
        for result in successful_results:
            category = result["predicted"]
            category_counts[category] = category_counts.get(category, 0) + 1
        
        for category, count in sorted(category_counts.items()):
            print(f"  {category}: {count} 条")
    
    # 6. 错误分析
    incorrect_results = [r for r in results if r["success"] and not r["correct"]]
    if incorrect_results:
        print(f"\n❌ 错误分类分析:")
        for result in incorrect_results:
            print(f"  文本: '{result['text'][:50]}...'")
            print(f"  期望: {result['expected']} → 预测: {result['predicted']}")
    
    # 7. 验证结果
    print(f"\n✅ 测试完成")
    
    if accuracy >= 70:  # 70%准确率
        print("🎉 测试通过！新分类配置工作正常")
        return True
    else:
        print("⚠️ 测试未完全通过，分类准确率需要提升")
        return False

if __name__ == "__main__":
    success = test_new_categories()
    if success:
        print("\n" + "=" * 60)
        print("🎯 测试程序02执行成功")
        print("✅ 新分类配置验证通过")
        print("📝 支持的分类：美食、饮品、景点、体育、新闻、娱乐等偏好")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 测试程序02执行失败")
        print("🔧 请检查分类配置和模型性能")
        print("=" * 60)
