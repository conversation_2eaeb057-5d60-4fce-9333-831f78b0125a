# OpenAI API Key
OPENAI_API_KEY=your-api-key-here

# Optional: Custom model names
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_COMPLETION_MODEL=gpt-4-turbo-preview

# PGVector Configuration (optional)
# Uncomment and set these values to use PGVector
#PGVECTOR_DB=vectordb
#PGVECTOR_USER=postgres
#PGVECTOR_PASSWORD=postgres
#PGVECTOR_HOST=localhost
#PGVECTOR_PORT=5432

# Qdrant Configuration (optional)
# Uncomment and set these values to use Qdrant
# QDRANT_URL=http://localhost:6333
#QDRANT_API_KEY=your-api-key-here
#QDRANT_PATH=/path/to/local/storage # For local file-based storage
#QDRANT_HOST=localhost # Alternative to URL
#QDRANT_PORT=6333 # Alternative to URL

# Redis Configuration (optional)
# Uncomment and set these values to use Redis
# REDIS_URL=redis://localhost:6379
# REDIS_USERNAME=default
# REDIS_PASSWORD=your-password-here 