{"name": "mem0ai-oss", "version": "1.0.0", "description": "TypeScript implementation of mem0 memory system", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "start": "pnpm run example memory", "example": "ts-node examples/vector-stores/index.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run build"}, "dependencies": {"@anthropic-ai/sdk": "^0.18.0", "@google/genai": "^0.7.0", "@qdrant/js-client-rest": "^1.13.0", "@types/node": "^20.11.19", "@types/pg": "^8.11.0", "@types/redis": "^4.0.10", "@types/sqlite3": "^3.1.11", "@types/uuid": "^9.0.8", "cloudflare": "^4.2.0", "dotenv": "^16.4.4", "groq-sdk": "^0.3.0", "openai": "^4.28.0", "pg": "^8.11.3", "redis": "^4.7.0", "sqlite3": "^5.1.7", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250504.0", "@types/jest": "^29.5.12", "jest": "^29.7.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "keywords": ["memory", "openai", "embeddings", "vector-store", "typescript"], "author": "", "license": "MIT"}